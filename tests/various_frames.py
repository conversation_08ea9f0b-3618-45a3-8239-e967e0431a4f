import tkinter
from tkinter import ttk

def create_label_frame(parent, text, bg):
    frame = ttk.Frame(parent, style='Custom.TFrame')
    label = tkinter.Label(frame, text=text, bg=bg, fg='white')
    label.pack(expand=True, fill='both')
    return frame

root = tkinter.Tk()
root.title("Pack方式布局")
root.geometry("600x400")

# 样式设置
style = ttk.Style()
style.configure("Custom.TFrame", background="gray")

# 主容器（垂直布局）
main_frame = ttk.Frame(root)
main_frame.pack(fill='both', expand=True, padx=10, pady=10)

# A - 顶部整行
frame_a = create_label_frame(main_frame, "A", "darkblue")
frame_a.pack(fill='x', expand=False)

# 中间区域容器（用于装B-1+C-1+D）
middle_frame = ttk.Frame(main_frame)
middle_frame.pack(fill='both', expand=True)

# 左侧容器（垂直放B-1和C-1）
left_frame = ttk.Frame(middle_frame)
left_frame.pack(side='left', fill='both', expand=True)

frame_b1 = create_label_frame(left_frame, "B-1", "darkgreen")
frame_b1.pack(fill='both', expand=True)

frame_c1 = create_label_frame(left_frame, "C-1", "darkred")
frame_c1.pack(fill='both', expand=True)

# 右侧D，占据B-1+C-1总高度
frame_d = create_label_frame(middle_frame, "D", "purple")
frame_d.pack(side='left', fill='both', expand=True)

# E - 底部整行
frame_e = create_label_frame(main_frame, "E", "darkorange")
frame_e.pack(fill='x', expand=False)

root.mainloop()
