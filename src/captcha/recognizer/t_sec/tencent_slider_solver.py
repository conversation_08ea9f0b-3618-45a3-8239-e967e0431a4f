"""腾讯滑块验证码专用解决器。

专门针对腾讯滑块验证码的识别和操作实现。
"""

import asyncio
import html
import io
import logging
import re
from typing import Optional, Tuple, Dict, Any

from PIL import Image
from playwright.async_api import async_playwright

from common.utils import logging_utils, img_utils
from .slider_captcha_recognizer import SliderCaptchaRecognizer

logger = logging.getLogger(__name__)

def _parse_style(style: str) -> Dict[str, Any]:
    """解析CSS样式字符串，提取背景图片相关属性。

    Args:
        style: CSS样式字符串

    Returns:
        包含解析结果的字典
    """
    s = html.unescape(style or "")
    res = {}

    # 提取 background-image URL
    m = re.search(r'background-image\s*:\s*url\((["\']?)(.*?)\1\)', s, flags=re.I)
    res['background_image'] = m.group(2) if m else None

    # 提取 background-position
    m = re.search(r'background-position\s*:\s*([-+]?\d+(\.\d+)?)px\s+([-+]?\d+(\.\d+)?)px', s, flags=re.I)
    if m:
        res['bg_pos_x'] = float(m.group(1))
        res['bg_pos_y'] = float(m.group(3))
    else:
        res['bg_pos_x'] = res['bg_pos_y'] = None

    # 提取 background-size
    m = re.search(r'background-size\s*:\s*([-+]?\d+(\.\d+)?)px\s+([-+]?\d+(\.\d+)?)px', s, flags=re.I)
    if m:
        res['bg_size_w'] = float(m.group(1))
        res['bg_size_h'] = float(m.group(3))
    else:
        res['bg_size_w'] = res['bg_size_h'] = None

    # 提取 width 和 height
    m = re.search(r'\bwidth\s*:\s*([-+]?\d+(\.\d+)?)px', s, flags=re.I)
    res['style_width'] = float(m.group(1)) if m else None

    m = re.search(r'\bheight\s*:\s*([-+]?\d+(\.\d+)?)px', s, flags=re.I)
    res['style_height'] = float(m.group(1)) if m else None

    return res


def _crop_from_background(image_bytes: bytes, css_bg_w: float, css_bg_h: float,
                         bg_pos_x: float, bg_pos_y: float, elem_w_css: float, elem_h_css: float) -> Image.Image:
    """从背景图片中裁剪出滑块部分。

    Args:
        image_bytes: 背景图片字节数据
        css_bg_w: CSS中定义的背景图片宽度
        css_bg_h: CSS中定义的背景图片高度
        bg_pos_x: 背景位置X坐标
        bg_pos_y: 背景位置Y坐标
        elem_w_css: 元素CSS宽度
        elem_h_css: 元素CSS高度

    Returns:
        裁剪后的滑块图片
    """
    img = Image.open(io.BytesIO(image_bytes)).convert("RGBA")
    actual_w, actual_h = img.size

    # 如果没有指定背景尺寸，使用实际图片尺寸
    if css_bg_w is None or css_bg_h is None:
        css_bg_w, css_bg_h = actual_w, actual_h

    # 计算缩放比例
    sx = actual_w / float(css_bg_w)
    sy = actual_h / float(css_bg_h)

    # 计算裁剪区域
    start_x = abs(bg_pos_x) * sx
    start_y = abs(bg_pos_y) * sy
    crop_w = elem_w_css * sx
    crop_h = elem_h_css * sy

    # 确保裁剪区域在图片范围内
    left = int(round(max(0, start_x)))
    upper = int(round(max(0, start_y)))
    right = int(round(min(actual_w, left + crop_w)))
    lower = int(round(min(actual_h, upper + crop_h)))

    if right <= left or lower <= upper:
        raise RuntimeError(f"空裁切框: {(left,upper,right,lower)} img_size={(actual_w,actual_h)}")

    return img.crop((left, upper, right, lower))


class TencentSliderSolver(SliderCaptchaRecognizer):
    """腾讯滑块验证码解决器。

    继承 SliderCaptchaRecognizer，专门用于解决腾讯滑块验证码的自动识别和操作。
    """
    
    def __init__(self,
                 ddddocr_base_url: str = "http://10.168.1.201:7777",
                 headless: bool = False,
                 browser_type: str = "chromium"):
        """初始化腾讯滑块验证码解决器。

        Args:
            ddddocr_base_url: ddddocr服务的基础URL
            headless: 是否使用无头模式运行浏览器
            browser_type: 浏览器类型
        """
        # 调用父类初始化
        super().__init__(ddddocr_base_url, headless, browser_type)

        logging_utils.logger_print(
            msg="initialized tencent slider solver",
            custom_logger=logger
        )
    

    async def start(self):
        """启动浏览器。"""
        self.playwright = await async_playwright().start()
        
        if self.browser_type == "firefox":
            browser_launcher = self.playwright.firefox
        elif self.browser_type == "webkit":
            browser_launcher = self.playwright.webkit
        else:
            browser_launcher = self.playwright.chromium
        
        self.browser = await browser_launcher.launch(
            headless=self.headless,
            args=[
                '--no-sandbox',
                '--disable-blink-features=AutomationControlled',
                '--disable-web-security'
            ]
        )
        
        self.context = await self.browser.new_context(
            viewport={'width': 1920, 'height': 1080},
            user_agent='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
        )
        
        self.page = await self.context.new_page()
        
        logging_utils.logger_print(
            msg="tencent slider solver browser started",
            custom_logger=logger
        )
    
    async def close(self):
        """关闭浏览器。"""
        if self.page:
            await self.page.close()
        if self.context:
            await self.context.close()
        if self.browser:
            await self.browser.close()
        if self.playwright:
            await self.playwright.stop()
        if self.ddddocr_client:
            self.ddddocr_client.close()
    
    async def navigate_to_page(self, url: str):
        """导航到目标页面。"""
        if not self.page:
            raise RuntimeError("browser not started")

        await self.page.goto(url, wait_until='domcontentloaded')
        await self.page.wait_for_timeout(2000)

        # 向下滚动以确保"立即体验"按钮可见
        logging_utils.logger_print(
            msg="scrolling page to make experience button visible",
            custom_logger=logger
        )
        await self.page.evaluate("() => window.scrollBy(0, window.innerHeight / 2)")
        await self.page.wait_for_timeout(500)
    
    async def wait_for_captcha(self, timeout: int = 30000) -> bool:
        """等待验证码出现。

        Args:
            timeout: 等待超时时间（毫秒）

        Returns:
            是否成功检测到验证码
        """
        try:
            logging_utils.logger_print(
                msg="waiting for tencent captcha to appear",
                custom_logger=logger
            )

            # 首先等待 iframe 出现（腾讯验证码通常在 iframe 中）
            try:
                await self.page.wait_for_selector('#tcaptcha_iframe_dy', timeout=timeout)
                logging_utils.logger_print(
                    msg="found captcha iframe",
                    custom_logger=logger
                )

                # 获取 iframe 元素
                iframe_element = await self.page.query_selector('#tcaptcha_iframe_dy')
                if iframe_element:
                    # 切换到 iframe 内容
                    iframe_content = await iframe_element.content_frame()
                    if iframe_content:
                        # 等待 iframe 内的验证码元素
                        await iframe_content.wait_for_selector('.tc-opera', timeout=15000)

                        # 保存主页面和 iframe 内容的引用
                        self.main_page = self.page
                        self.iframe_page = iframe_content

                        logging_utils.logger_print(
                            msg="successfully found captcha iframe, keeping main page as self.page",
                            custom_logger=logger
                        )

                        # 等待背景图和滑块加载完成（在 iframe 中）
                        await iframe_content.wait_for_selector('.tc-bg-img', timeout=5000)
                        await iframe_content.wait_for_selector('.tc-slider-normal', timeout=5000)

                        return True

            except Exception as iframe_error:
                logging_utils.logger_print(
                    msg=f"iframe approach failed, trying direct approach: {iframe_error}",
                    custom_logger=logger
                )

            # 如果 iframe 方法失败，尝试直接查找验证码元素
            try:
                await self.page.wait_for_selector('.tc-opera', timeout=timeout)
                await self.page.wait_for_selector('.tc-bg-img', timeout=5000)
                await self.page.wait_for_selector('.tc-slider-normal', timeout=5000)

                logging_utils.logger_print(
                    msg="tencent captcha detected (direct approach)",
                    custom_logger=logger
                )
                return True

            except Exception as direct_error:
                logging_utils.logger_print(
                    msg=f"direct approach failed: {direct_error}",
                    custom_logger=logger
                )

            return False

        except Exception as e:
            logging_utils.logger_print(
                msg="failed to detect tencent captcha",
                custom_logger=logger,
                use_exception=True,
                exception=e
            )
            return False
    
    async def get_captcha_images(self) -> Tuple[Optional[bytes], Optional[bytes]]:
        """获取验证码图片。

        使用改进的方法：
        1. 背景图：通过URL下载
        2. 滑块图：通过精确裁剪获取

        Returns:
            (背景图数据, 滑块图数据)
        """
        try:
            # 查找符合条件的滑块元素（background-position 两值均小于0）
            target_element = None
            parsed_meta = None

            logging_utils.logger_print(
                msg="searching for slider element with negative background-position",
                custom_logger=logger
            )

            # 如果当前page是Frame，则只搜索当前frame
            if hasattr(self.page, 'frames'):
                frames_to_search = self.page.frames
            else:
                # 当前page是Frame对象，只搜索自己
                frames_to_search = [self.page]

            # 如果有主页面，也搜索主页面的frames
            if hasattr(self, 'main_page') and self.main_page and hasattr(self.main_page, 'frames'):
                frames_to_search.extend(self.main_page.frames)

            for frame in frames_to_search:
                try:
                    # 查找所有 tc-fg-item 元素
                    elements = await frame.query_selector_all("div.tc-fg-item")
                    if not elements:
                        continue

                    for element in elements:
                        style = await element.get_attribute("style") or ""
                        parsed = _parse_style(style)

                        bx = parsed.get("bg_pos_x")
                        by = parsed.get("bg_pos_y")

                        if bx is None or by is None:
                            continue

                        # 只选择两个值均小于0的元素
                        if bx < 0 and by < 0:
                            logging_utils.logger_print(
                                msg=f"found target element: bg_pos=({bx}, {by})",
                                custom_logger=logger
                            )
                            target_element = element
                            parsed_meta = parsed
                            break

                    if target_element:
                        break

                except Exception as frame_error:
                    logging_utils.logger_print(
                        msg=f"error searching in frame: {frame_error}",
                        custom_logger=logger
                    )
                    continue

            if not target_element:
                logging_utils.logger_print(
                    msg="no slider element found with negative background-position",
                    custom_logger=logger
                )
                return None, None

            try:
                bbox = await target_element.bounding_box()
            except Exception:
                bbox = None

            elem_w = None
            elem_h = None
            if bbox and bbox.get("width") and bbox.get("height"):
                elem_w = bbox["width"]
                elem_h = bbox["height"]


            # 获取背景图片信息（从 #slideBg 元素）
            bg_url = None
            web_bg_show_width = None
            web_bg_show_height = None

            # 查找背景图元素
            for frame in frames_to_search:
                try:
                    bg_element = await frame.query_selector('#slideBg')
                    if bg_element:
                        bg_style = await bg_element.get_attribute('style')
                        if bg_style:
                            bg_parsed = _parse_style(bg_style)
                            bg_url = bg_parsed.get("background_image")
                            web_bg_show_width = bg_parsed.get("style_width")
                            web_bg_show_height = bg_parsed.get("style_height")
                            if bg_url:
                                logging_utils.logger_print(
                                    msg=f"found background image URL from #slideBg: {bg_url[:50]}...",
                                    custom_logger=logger
                                )
                                break
                except Exception as bg_search_error:
                    logging_utils.logger_print(
                        msg=f"error searching for background in frame: {bg_search_error}",
                        custom_logger=logger
                    )
                    continue

            if not bg_url:
                logging_utils.logger_print(
                    msg="no background image URL found in #slideBg element",
                    custom_logger=logger
                )
                return None, None

            logging_utils.logger_print(
                msg=f"downloading background image: {bg_url[:50]}...",
                custom_logger=logger
            )

            # 使用 context.request 下载图片（携带会话cookie）
            try:
                response = await self.context.request.get(bg_url, timeout=30000)
                if response.status != 200:
                    # 重试一次
                    response = await self.context.request.get(bg_url, timeout=30000)
                    if response.status != 200:
                        logging_utils.logger_print(
                            msg=f"failed to download background image: HTTP {response.status}",
                            custom_logger=logger
                        )
                        return None, None

                bg_data = await response.body()
                # bg_data调整成网页大小
                bg_image=Image.open(io.BytesIO(bg_data))
                original_format = bg_image.format
                resized_image = img_utils.resize_image_pillow(bg_image, web_bg_show_width, web_bg_show_height)
                img_byte_arr = io.BytesIO()
                if original_format == 'JPEG' and resized_image.mode in ('RGBA', 'LA'):
                    resized_image = resized_image.convert('RGB')
                    resized_image.save(img_byte_arr, format='JPEG')
                else:
                    resized_image.save(img_byte_arr, format=original_format)

                bg_data=img_byte_arr.getvalue()

                logging_utils.logger_print(
                    msg=f"background image downloaded: {len(bg_data)} bytes",
                    custom_logger=logger
                )

            except Exception as download_error:
                logging_utils.logger_print(
                    msg=f"error downloading background image: {download_error}",
                    custom_logger=logger
                )
                return None, None

            # 获取滑块图片（从滑块元素的背景图裁剪）
            slider_data = None
            slider_bg_url = parsed_meta.get("background_image")
            bg_pos_x = parsed_meta.get("bg_pos_x")
            bg_pos_y = parsed_meta.get("bg_pos_y")
            bg_size_w = parsed_meta.get("bg_size_w")
            bg_size_h = parsed_meta.get("bg_size_h")

            if slider_bg_url:
                logging_utils.logger_print(
                    msg=f"downloading slider background image: {slider_bg_url[:50]}...",
                    custom_logger=logger
                )

                try:
                    # 下载滑块的背景图
                    slider_response = await self.context.request.get(slider_bg_url, timeout=30000)
                    if slider_response.status == 200:
                        slider_bg_data = await slider_response.body()

                        # 裁剪滑块图片
                        cropped_slider = _crop_from_background(
                            slider_bg_data, bg_size_w, bg_size_h, bg_pos_x, bg_pos_y, elem_w, elem_h
                        )
                        web_show_width = parsed_meta.get("style_width")
                        web_show_height = parsed_meta.get("style_height")
                        #调整成网页实际现实的大小
                        cropped_slider=img_utils.resize_image_pillow(cropped_slider, web_show_width, web_show_height)
                        # 转换为字节数据
                        buffer = io.BytesIO()
                        cropped_slider.save(buffer, format='PNG')
                        slider_data = buffer.getvalue()

                        logging_utils.logger_print(
                            msg=f"slider image cropped successfully: {len(slider_data)} bytes, size={cropped_slider.size}",
                            custom_logger=logger
                        )
                    else:
                        logging_utils.logger_print(
                            msg=f"failed to download slider background: HTTP {slider_response.status}",
                            custom_logger=logger
                        )

                except Exception as slider_error:
                    logging_utils.logger_print(
                        msg=f"error processing slider image: {slider_error}",
                        custom_logger=logger
                    )
            else:
                logging_utils.logger_print(
                    msg="no slider background image URL found",
                    custom_logger=logger
                )

            return bg_data, slider_data

        except Exception as e:
            logging_utils.logger_print(
                msg="error getting captcha images",
                custom_logger=logger,
                use_exception=True,
                exception=e
            )
            return None, None
    



    async def solve_captcha(self, max_retries: int = 3) -> bool:
        """解决腾讯滑块验证码。
        使用基类的 solve_slider_captcha 方法。

        Args:
            max_retries: 最大重试次数

        Returns:
            是否成功解决验证码
        """
        for attempt in range(max_retries):
            logging_utils.logger_print(
                msg=f"solving tencent captcha (attempt {attempt + 1}/{max_retries})",
                custom_logger=logger
            )

            try:
                # 等待验证码加载
                if not await self.wait_for_captcha():
                    continue

                # 获取验证码图片
                slider_data, bg_data = await self.get_slider_images()
                if not bg_data:
                    logging_utils.logger_print(
                        msg="failed to get captcha images",
                        custom_logger=logger
                    )
                    continue

                # 腾讯验证码特殊处理：临时切换到 iframe 进行拖拽操作
                original_page = self.page
                if hasattr(self, 'iframe_page') and self.iframe_page:
                    # 临时切换到 iframe 页面进行拖拽
                    self.page = self.iframe_page
                    logging_utils.logger_print(
                        msg="temporarily switched to iframe for drag operation",
                        custom_logger=logger
                    )

                try:
                    # 使用基类的 solve_slider_captcha 方法
                    success = await self.solve_slider_captcha(
                        slider_data=slider_data,
                        background_data=bg_data,
                        background_selector='#slideBg',
                        slider_button_selector='.tc-slider-normal',
                        slider_button_x_offset=0,
                        use_slide_comparison=False  # 使用 capcode API，推荐用于腾讯验证码
                    )
                finally:
                    # 恢复原始页面
                    self.page = original_page
                    logging_utils.logger_print(
                        msg="restored original page after drag operation",
                        custom_logger=logger
                    )

                if success:
                    # 等待验证结果
                    result = await self._wait_for_result()
                    if result:
                        logging_utils.logger_print(
                            msg="tencent captcha solved successfully",
                            custom_logger=logger
                        )
                        return True

                # 如果失败，等待一段时间后重试
                await asyncio.sleep(2)

            except Exception as e:
                logging_utils.logger_print(
                    msg=f"error in attempt {attempt + 1}",
                    custom_logger=logger,
                    use_exception=True,
                    exception=e
                )

        logging_utils.logger_print(
            msg=f"failed to solve tencent captcha after {max_retries} attempts",
            custom_logger=logger
        )
        return False

    async def _wait_for_result(self, timeout: int = 10000) -> Optional[bool]:
        """等待验证结果。"""
        try:
            # 等待成功或失败状态
            result = await self.page.wait_for_selector(
                '.tc-success, .tc-error',
                timeout=timeout
            )
            
            if result:
                class_name = await result.get_attribute('class')
                if 'tc-success' in class_name:
                    return True
                elif 'tc-error' in class_name:
                    return False
            
            return None
            
        except Exception:
            return None

    async def get_slider_images(self,
                               background_selector: str = '#slideBg',
                               slider_selector: Optional[str] = None) -> Tuple[bytes, bytes]:
        """获取滑块验证码的背景图和滑块图。
        重写父类方法，专门用于腾讯滑块验证码的图片获取。

        Args:
            background_selector: 背景图元素选择器，默认为腾讯验证码的背景图选择器
            slider_selector: 滑块图元素选择器，对于腾讯验证码通常不需要

        Returns:
            (滑块图数据, 背景图数据)：两个 bytes 对象，分别表示滑块图片和背景图片
        """
        logging_utils.logger_print(
            msg=f"getting tencent slider images with background_selector: {background_selector}, slider_selector: {slider_selector}",
            custom_logger=logger
        )

        try:
            # 调用内部的获取图片方法，腾讯验证码有特殊的获取逻辑
            bg_data, slider_data = await self.get_captcha_images()

            if bg_data is None:
                raise RuntimeError("failed to get background image")

            # 如果没有滑块图，使用背景图作为滑块图
            if slider_data is None:
                logging_utils.logger_print(
                    msg="no slider image found, using background image as fallback",
                    custom_logger=logger
                )
                slider_data = bg_data

            logging_utils.logger_print(
                msg=f"successfully got slider images: slider={len(slider_data)} bytes, background={len(bg_data)} bytes",
                custom_logger=logger
            )

            # 返回格式：(滑块图数据, 背景图数据)
            return slider_data, bg_data

        except Exception as e:
            logging_utils.logger_print(
                msg="error getting slider images",
                custom_logger=logger,
                use_exception=True,
                exception=e
            )
            raise


