"""服务器控制组件。

负责服务器的启动、停止和状态管理。
由于其启动涉及到 配置项 runtime,在当前时间点不允许运行的情况下,就会弹出是否修改运行时段的提示框 --- 进入服务端配置项修改界面
启动之后会刷新数据显示区域，但由于其和当前嵌合的比较紧密，不方便分离到data_table中，所以代码实现直接放到这里
"""
import logging
import multiprocessing
import threading
import time
from datetime import datetime
from tkinter import messagebox
from typing import Optional

import ttkbootstrap as ttkb
from ttkbootstrap.constants import *

from common.constants import const
from common.utils import time_utils, gui_utils, logging_utils
from webhook_server.config import constants, config_check
from webhook_server.gui import status_monitor, data_table
from webhook_server.gui.menu_dialogs import config_dialog
from webhook_server.models import gui_properties, server_data_manager
from webhook_server.utils import webhook_server_utils


class ServerControlComponent:
    """服务器控制组件类。"""

    def __init__(self, props: gui_properties.GUIProperties, server_config_dialog_co:config_dialog.ConfigDialogComponent, status_monitor_co:status_monitor.StatusMonitorComponent, data_table_co:data_table.DataTableComponent):
        """初始化服务器控制组件
        用于控制服务端启动、停止

        Args:
            props: GUI属性
            server_config_dialog_co: 服务端配置界面
            status_monitor_co: 服务端运行状态监控
            data_table_co: 数据显示区域
        """
        self.props = props
        self.server_config_dialog_co = server_config_dialog_co
        self.status_monitor_co = status_monitor_co
        self.data_table_co = data_table_co
        # 性能优化：减少不必要的UI更新
        self.last_ui_update = 0
        self.ui_update_interval = 1.0  # 1秒更新一次UI
        self.start_btn: Optional[ttkb.Button] = None
        self.stop_btn: Optional[ttkb.Button] = None
        self.server_refresh_thread: Optional[threading.Thread] = None # 后台更新服务端状态信息线程
        self.had_initialized_startup=False # 是否已经执行过第一次启动服务端
        self.stop_server_process_event=multiprocessing.Event()

    def create_widgets(self):
        """创建服务端运行控制组件 --- 运行按钮、停止按钮"""
        # 按钮区域（固定在底部）side=ttkb.BOTTOM,  .pack(fill=X, expand=False)
        btn_frame = ttkb.Frame(self.props.root)
        btn_frame.pack(fill=X, expand=False, padx=20, pady=(0,5))
        self.start_btn = ttkb.Button(btn_frame, text="启动服务端", command=self.start_server)
        self.start_btn.pack(side=LEFT, padx=10)
        self.stop_btn = ttkb.Button(btn_frame, text="停止服务端", command=self.stop_server, state=DISABLED)
        self.stop_btn.pack(side=LEFT, padx=10)

    def start_server(self):
        self.props.logger.info("starting server process")

        self.props.error_occured=False
        # 保存前检测服务端配置项是否有效
        self.check_all_config_before_start()
        if self.props.error_occured:
            self.props.logger.error("configuration check failed, aborting server start")
            return

        # 检查运行时间段
        if not self._check_runtime_allowed():
            return

        try:
            self.props.logger.info("creating server process with config path: %s", self.props.config_file_path)

            # 启动服务端进程
            self.stop_server_process_event.clear()

            self.props.server_process = webhook_server_utils.start_independent_process(config_path=self.props.config_file_path, argument_param='--webhook', relative_script_path='./src/webhook_server/models/child_webhook_server.py')
            self.props.logger.info("server process started with pid: %s", self.props.server_process.pid)

            if self.props.server_process.poll() is not None:
                raise ValueError("启动服务端进程失败!")

            self.props.logger.info("server process is alive, updating ui state")
            self.update_start_btn_state(False)
            self.status_monitor_co.status_label.config(text="服务端运行中",foreground="green")
            self.data_table_co.data_ui_no_show(show_msg="服务端正在启动,请稍候...")

            # 启动服务端进程监控器
            self.props.logger.info(f"starting server process monitor for pid: {self.props.server_process.pid}")
            self.status_monitor_co.start_server_process_monitor()

            self.props.logger.info("initializing data manager")
            self.props.message_data_manager = server_data_manager.WebhookDataManager(db_path=constants.CROSS_PROCESS_DATA_BASE_PATH,table_name=self.props.server_config_values["message_data_table_name"], zone=self.props.config_file_time_zone, enable_sql_logging=bool(self.props.server_config_values["enable_sql_logging"]))
            self.props.logger.info("scheduling server status check in 1 second")
            self.start_server_refresh_thread()
            self.props.logger.info("server start process completed successfully")
        except Exception as start_server_exp:
            self.props.logger.exception("server start process failed!", exc_info=True)
            self.props.show_operation_error(error=start_server_exp, error_msg="启动服务端失败",parent_gui=self.props.root)
            # 清理资源
            self.status_monitor_co.stop_server_process_monitor()
            self.props.server_process = None
            self.update_start_btn_state(True)
            self.status_monitor_co.status_label.config(text="服务端启动失败",foreground="red")

    def stop_server(self):
        self.stop_server_process()
        self.stop_server_refresh_thread()

    def stop_server_refresh_thread(self):
        """停止服务端时停止后台刷新数据和前端刷新ui"""
        self.status_monitor_co.stop_server_event.set()
        self.stop_server_ui()
        self._stop_server_refresh_thread()

    def check_all_config_before_start(self):
        # 在服务端正式启动前,进行配置项的校验 --- 不需要进行保存
        if self.props.server_process_alive():
            self.props.show_operation_error(error=None,error_msg= "服务端正在运行,请不要重复启动服务端!",parent_gui=self.props.root)
            return
        self.props.error_occured=False
        # 用户自定义配置项不能为空
        try:
            config_check.check_server_config_file_to_runtime_webhook(self.props.config_file_path)
        except Exception as check_config_file_except:
            self.props.show_operation_error(error=check_config_file_except,error_msg= str(check_config_file_except),parent_gui=self.props.root)
        # 阻断后续执行
        if self.props.error_occured:
            return

    def _check_runtime_allowed(self)->bool:
        """检查当前时间是否在允许的运行时间段内"""
        try:
            run_time = self.props.server_config_values.get("run_time", "07:00-07:00")
            can_run, always_run, start_time, end_time, now = time_utils.check_runtime_allowed(run_time, self.props.config_file_time_zone)

            if not can_run:
                # 计算下次可运行时间
                next_run_info = ServerControlComponent._calculate_next_run_time(start_time, end_time, now)
                error_msg = f"⏰ 服务端运行时间限制\n\n当前时间: {now.strftime('%H:%M')}\n运行时间段: {run_time}\n\n{next_run_info}\n\n💡 提示: 如需修改运行时间段，请前往 [设置] → [服务端配置] 修改"

                # 显示错误对话框，并询问是否要打开配置
                result = messagebox.askyesnocancel(
                    "服务端运行时间限制",
                    error_msg + "\n\n是否现在打开服务端配置进行修改？",
                    parent=self.props.root
                )

                if result:  # 用户选择"是"
                    self.server_config_dialog_co.open_config_dialog()

                self.props.logger.warning(f"server start blocked: current time {now} not in allowed runtime {run_time}")
                return False

            return True

        except Exception as e:
            self.props.logger.exception("error checking runtime allowance")
            self.props.show_operation_error(error=e, error_msg=f"检查运行时间段时出错: {e}", parent_gui=self.props.root)
            return False

    @staticmethod
    def _calculate_next_run_time(start_time, end_time, now):
        """计算下次可运行时间的提示信息"""
        # 如果是跨天运行,那么在当前不能运行的情况下,其肯定是今天的开始时间
        # 如果当前时间在今天的结束时间之后且当前是跨天的,说明已经在运行时段内了
        # 跨天或当前时间早于开始时间 => 今天的开始时间
        if end_time < start_time or now < start_time:
            day = "今天"
        else:
            # 正常情况且当前时间已过结束时间 => 明天的开始时间
            day = "明天"
        return f"下次可运行时间: {day} {start_time.strftime('%H:%M')}"

    def start_server_refresh_thread(self):
        """启动服务端时启动后台刷新数据+前端刷新ui"""
        if hasattr(self, 'server_refresh_thread') and self.server_refresh_thread is not None and self.server_refresh_thread.is_alive():
            return
        self.status_monitor_co.stop_server_event.clear()
        # 等待服务端启动完成
        self.wait_started()
        self.server_refresh_thread = threading.Thread(target=self.status_monitor_co.update_server_status,daemon=True,name="gui_webhook_server_refresh_thread")
        self.server_refresh_thread.start()
        self.props.root.after(1000, self.refresh_ui)  # type: ignore

    def wait_started(self):
        """等待服务端启动完成
        args:
            is_thread: 是否在后台线程中执行[update_server_status],默认False,表示在主线程中执行
        """
        # 服务端刚刚启动,其状态信息可能还未初始化,因此需要等待一段时间
        while not self.status_monitor_co.stop_server_event.is_set():
            if self.props.server_process_alive():
                # server_start_time用来计算服务端运行时长
                self.status_monitor_co.server_start_time=time.monotonic() - self.status_monitor_co.total_runtime_seconds
                break
            self.status_monitor_co.stop_server_event.wait(1)


    def refresh_ui(self):
        """根据后台线程更新的服务端状态信息,刷新ui界面,在服务端启动之后执行"""
        # 在这里不进行进程的检测[_server_process_alive],由于其已经在 update_server_status 中进行了检测,如果这里继续进行检测,可能会出现这里的ui先停止更新,而后台线程还在更新没有将最后一次的状态信息更新到gui_server_info,导致ui显示不正确
        if not hasattr(self.props, 'root') or not gui_utils.gui_exist(self.props.root) or not hasattr(self.status_monitor_co, 'stop_server_event') or self.status_monitor_co.stop_server_event.is_set():
            self.stop_server_refresh_thread()
            return

        # 等待后台刷新数据然后更新ui
        current_gui_server_info=getattr(self.status_monitor_co, 'gui_server_info',None)
        # 等待后台刷新数据完成
        if not current_gui_server_info:
            self.props.root.after(500, self.refresh_ui) # type: ignore
            return
        if not self.had_initialized_startup:
            self.status_monitor_co.server_initial_startup_time.config(text=f"服务端初始启动时间: {datetime.now(self.props.config_file_time_zone).strftime(const.DATETIME_FORMAT)}")
            self.had_initialized_startup=True

        self.update_start_btn_state(active=current_gui_server_info.server_button_flag)

        self.status_monitor_co.refresh_ui(current_gui_server_info)
        self.data_table_co.refresh_ui(current_gui_server_info)

        # 动态调整刷新频率
        current_time = time.time()
        if current_time - self.last_ui_update >= self.ui_update_interval:
            self.last_ui_update = current_time
            next_update_delay = 1000  # 正常刷新间隔
        else:
            next_update_delay = 500   # 快速刷新间隔

        self.props.root.after(next_update_delay, self.refresh_ui) # type: ignore

    def stop_server_ui(self):
        """在服务端停止时ui样式"""
        self.status_monitor_co.stop_server_event.set()
        if hasattr(self.status_monitor_co, 'gui_server_info') and self.status_monitor_co.gui_server_info is not None:
            cur_gui_server_info = self.status_monitor_co.gui_server_info
            self.status_monitor_co.status_label.config(text=cur_gui_server_info.server_status_message,foreground=cur_gui_server_info.server_status_color)
            exit_code=getattr(cur_gui_server_info, 'exit_code', None)
            if exit_code is not None and exit_code!=0:
                self.props.show_operation_error(error=None, error_msg=cur_gui_server_info.exit_reason,parent_gui=self.props.root)
            if cur_gui_server_info.stop_server_flag:
                cur_gui_server_info.stop_server_flag=False
                self._stop_server_refresh_thread()
                self.stop_server_process()
        else:
            self.status_monitor_co.status_label.config(text="服务端已停止",foreground="black")
        self.status_monitor_co.gui_server_info=None
        self.data_table_co.clear_data()
        self.data_table_co.data_ui_no_show(show_msg="服务端停止不显示数据")
        self.status_monitor_co.reset_system_monitor_info()
        self.update_start_btn_state(True)

    def _stop_server_refresh_thread(self):
        if hasattr(self, 'server_refresh_thread') and self.server_refresh_thread is not None and self.server_refresh_thread.is_alive():
            try:
                self.props.logger.info("waiting for server refresh thread to finish")
                self.server_refresh_thread.join(timeout=5.0)
                if self.server_refresh_thread.is_alive():
                    self.props.logger.warning("server refresh thread did not finish within timeout")
                else:
                    self.props.logger.info("server refresh thread finished successfully")
            except Exception as thread_error:
                logging_utils.logger_print(msg=f"error joining server refresh thread: {thread_error}", custom_logger=self.props.logger, use_exception=True, exception=thread_error)
            finally:
                self.server_refresh_thread = None

    def stop_server_process(self):
        """停止服务端子进程"""
        if not hasattr(self.props, 'root') or not gui_utils.gui_exist(self.props.root) or not self.props.server_process_alive():
            logging_utils.logger_print(msg="no running server process found", custom_logger=self.props.logger)
            return
        logging_utils.logger_print(msg="stopping server process", custom_logger=self.props.logger)
        logging_utils.logger_print(msg=f"terminating server process (pid: {self.props.server_process.pid})", custom_logger=self.props.logger)

        # 停止服务端进程监控器
        self.status_monitor_co.stop_server_process_monitor()

        # 终止服务端进程
        self.stop_server_process_event.set()
        self.props.server_process.terminate()
        self.props.server_process.wait(timeout=5.0)
        if self.props.server_process_alive():
            logging_utils.logger_print(msg="force killing server process", custom_logger=self.props.logger, log_level=logging.WARNING)
            self.props.server_process.kill()
        self.status_monitor_co.status_label.config(text="服务端已停止",foreground="black")
        self.props.server_process = None


    def update_start_btn_state(self,active:bool) -> None:
        """
        更新启动/停止按钮状态
        Args: active: 是否激活启动按钮 (True=启动可用, False=停止可用)
        """
        if hasattr(self,'start_btn') and hasattr(self,'stop_btn'):
            self.start_btn.config(state=NORMAL if active else DISABLED)
            self.stop_btn.config(state=DISABLED if active else NORMAL)
