#!/usr/bin/env python3
"""
测试打包环境和subprocess功能的脚本
"""

import os
import platform
import subprocess
import sys
import tempfile
from pathlib import Path

# 添加src路径以便导入gui_constants
sys.path.insert(0, str(Path(__file__).parent.parent.parent / "src"))
from common.constants import const


def is_pyinstaller_bundle() -> bool:
    """检测是否在 PyInstaller 打包环境中运行"""
    return getattr(sys, 'frozen', False) and hasattr(sys, '_MEIPASS')

def test_environment():
    """测试打包环境"""
    print("=" * 50)
    print("NexusRecv 打包环境测试")
    print("=" * 50)

    # Python版本
    print(f"Python版本: {sys.version}")
    print(f"Python路径: {sys.executable}")
    print(f"PyInstaller环境: {is_pyinstaller_bundle()}")
    print(f"临时目录: {tempfile.gettempdir()}")
    print(f"_MEIPASS: {getattr(sys, '_MEIPASS', 'None')}")

    # 平台信息
    print(f"操作系统: {const.CURRENT_SYSTEM}")
    print(f"平台: {platform.platform()}")
    print(f"架构: {platform.architecture()}")
    print(f"当前平台常量: {const.CURRENT_PLATFORM}")
    print(f"是否Windows: {const.IS_WINDOWS}")
    print(f"是否Linux: {const.IS_LINUX}")
    print(f"是否macOS: {const.IS_MACOS}")

    # 项目路径
    project_root = Path(__file__).parent.parent.parent.absolute()
    print(f"项目根目录: {project_root}")

    # 检查关键文件
    key_files = [
        "src/webhook_server/config_selection_gui.py",
        "src/webhook_server/webhook_server_gui.py",
        "resources/22.png",
        "webhook_server_gui.spec",
        "requirements.txt",
        "scripts/build/build_by_platform.py",
        "scripts/build/build_windows.py",
        "scripts/build/build_linux.py",
        "scripts/build/requirements_build.txt"
    ]

    print("\n关键文件检查:")
    for file_path in key_files:
        full_path = project_root / file_path
        status = "✓" if full_path.exists() else "✗"
        print(f"  {status} {file_path}")

    # 检查依赖
    print("\n基础依赖检查:")
    dependencies = [
        "pathlib",
        "configparser",
        "multiprocessing",
        "tkinter"
    ]

    for dep in dependencies:
        try:
            __import__(dep)
            print(f"  ✓ {dep}")
        except ImportError:
            print(f"  ✗ {dep}")

    # 检查可选依赖
    print("\n打包依赖检查:")
    optional_deps = [
        "PyInstaller",
        "ttkbootstrap",
        "fastapi",
        "uvicorn",
        "requests",
        "aiohttp"
    ]

    for dep in optional_deps:
        try:
            module = __import__(dep)
            version = getattr(module, '__version__', 'unknown')
            print(f"  ✓ {dep} ({version})")
        except ImportError:
            print(f"  ✗ {dep} (未安装)")

def test_subprocess():
    """测试subprocess功能"""
    print("\n" + "=" * 50)
    print("Subprocess 功能测试")
    print("=" * 50)
    print(f"当前进程PID: {os.getpid()}")

    # 测试不设置环境变量的子进程
    print("\n测试1: 不设置PYINSTALLER_RESET_ENVIRONMENT")
    child_script = 'import os, sys, tempfile; print(f"子进程PID: {os.getpid()}, 临时目录: {tempfile.gettempdir()}, _MEIPASS: {getattr(sys, \'_MEIPASS\', \'None\')}, RESET_ENV: {os.environ.get(\'PYINSTALLER_RESET_ENVIRONMENT\', \'Not Set\')}")'

    try:
        result = subprocess.run(
            [sys.executable, "-c", child_script],
            capture_output=True,
            text=True,
            timeout=10
        )
        print(f"输出: {result.stdout.strip()}")
        if result.stderr:
            print(f"错误: {result.stderr.strip()}")
    except Exception as e:
        print(f"执行失败: {e}")

    # 测试设置环境变量的子进程
    print("\n测试2: 设置PYINSTALLER_RESET_ENVIRONMENT=1")
    env = os.environ.copy()
    env['PYINSTALLER_RESET_ENVIRONMENT'] = '1'

    try:
        result = subprocess.run(
            [sys.executable, "-c", child_script],
            capture_output=True,
            text=True,
            timeout=10,
            env=env
        )
        print(f"输出: {result.stdout.strip()}")
        if result.stderr:
            print(f"错误: {result.stderr.strip()}")
    except Exception as e:
        print(f"执行失败: {e}")

def main():
    """主函数"""
    test_environment()
    test_subprocess()

    print("\n" + "=" * 50)
    print("所有测试完成")
    print("=" * 50)

if __name__ == "__main__":
    main()
