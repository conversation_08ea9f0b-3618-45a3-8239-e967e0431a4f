"""单例元类模块。

此模块提供了单例模式的元类实现，确保使用此元类的类
在一个进程中只能被实例化一次。
"""

from typing import Any


class SingleInstanceMeta(type):
    """单例元类，确保使用此元类的类在一个进程中只能被实例化一次。

    第二次尝试实例化时会抛出异常，用于确保某些关键类的唯一性。

    Attributes:
        _instance: 用于存储实例的引用
    """

    def __init__(cls, name: str, bases: tuple, namespace: dict) -> None:
        """初始化元类。

        Args:
            name: 类名
            bases: 基类元组
            namespace: 类命名空间
        """
        super().__init__(name, bases, namespace)
        cls._instance = None  # 用于存储实例的引用

    def __call__(cls, *args: Any, **kwargs: Any) -> Any:
        """控制类的实例化过程。

        Args:
            *args: 位置参数
            **kwargs: 关键字参数

        Returns:
            Any: 类的实例

        Raises:
            RuntimeError: 当类已经被实例化过时抛出异常
        """
        if cls._instance is not None:
            raise RuntimeError(f"{cls.__name__} 已经被实例化过，不能重复创建")

        # 创建实例
        cls._instance = super().__call__(*args, **kwargs)
        return cls._instance
