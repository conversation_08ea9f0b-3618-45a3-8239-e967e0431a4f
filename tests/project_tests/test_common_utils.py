#!/usr/bin/env python3
"""
测试公共工具模块
提取webhook_server_test和server_config_gui_test中的公共代码
"""

import os
import sqlite3
import sys
from zoneinfo import ZoneInfo

import sys
import os
from pathlib import Path

# 添加src路径
src_path = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..', 'src'))
if src_path not in sys.path:
    sys.path.append(src_path)

from webhook_server.config.gui_constants import BASE_PATH
from webhook_server.config import constants
from webhook_server.utils import config_lock
from common.utils import shutdown_exec_funct

config_manager = config_lock.MultiProcessConfigManager.get_singleton_instance(
    db_path=constants.CROSS_PROCESS_DATA_BASE_PATH,
    enable_sql_logging=False,
    zone=ZoneInfo("Asia/Shanghai")
)
def setup_test_environment():
    """设置测试环境变量"""
    os.environ['PYTHONUNBUFFERED'] = '1'  # 确保输出不被缓存
    os.environ['TEST_MODE'] = '1'  # 标记为测试模式


class MockMessagebox:
    """模拟messagebox类，用于测试环境"""
    def __init__(self):
        self.error_messages = []
    
    def showerror(self, title, message, **kwargs):  # noqa
        error_text = "ERROR: " + str(title) + " - " + str(message)
        self.error_messages.append(error_text)
        print(error_text, file=sys.stderr)
        # 重要：抛出异常，确保程序不会继续执行
        raise Exception("配置错误: " + str(title) + " - " + str(message))


def setup_mock_messagebox():
    """设置模拟messagebox"""
    sys.modules['tkinter.messagebox'] = MockMessagebox()


def clean_database():
    """清空CROSS_PROCESS_DATA_BASE_PATH对应的数据库内所有表和表数据"""
    db_path = constants.CROSS_PROCESS_DATA_BASE_PATH
    print(f"清理数据库: {db_path}")
    
    if not os.path.exists(db_path):
        print(f"数据库文件不存在，无需清理: {db_path}")
        return
    
    try:
        # 连接数据库
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 获取所有表名
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
        tables = cursor.fetchall()
        
        if not tables:
            print("数据库中没有表，无需清理")
            return
        
        # 关闭外键约束
        cursor.execute("PRAGMA foreign_keys = OFF;")
        
        # 开始事务
        cursor.execute("BEGIN TRANSACTION;")
        
        # 删除每个表中的所有数据
        for table in tables:
            table_name = table[0]
            print(f"  - 删除表 '{table_name}' 中的所有数据")
            cursor.execute(f"DELETE FROM {table_name};")
        
        # 提交事务
        conn.commit()
        
        print(f"成功清理数据库中的所有表数据")
        
    except sqlite3.Error as e:
        print(f"清理数据库时出错: {e}")
    finally:
        if 'conn' in locals():
            conn.close()  # noqa


def insert_pre_need_data():
    """插入预先需要的数据:测试配置项项唯一性"""
    insert_sql = """
INSERT INTO "config_file_record" ("id", "name", "file_path", "process_id", "last_used", "checksum", "is_active", "fail_reason", "created_at", "updated_at") VALUES (1, '服务端配置_2', 'D:\Git\python-samples-hub\\tests\other_conf\server_config_test.ini', NULL, '2025-07-19 11:16:02', '4cfcd0f2b300b47adb22717a58d863d532190cea633c69e49f4b86c097d04b6e', 1, NULL, '2025-07-19 11:16:00', '2025-07-19 11:16:02');
INSERT INTO "config_unique_constraints" ("id", "config_file_record_id", "config_key", "config_value", "created_at", "updated_at") VALUES (1, 1, 'port', '8000', '2025-07-19 11:16:00', '2025-07-19 11:16:00');
INSERT INTO "config_unique_constraints" ("id", "config_file_record_id", "config_key", "config_value", "created_at", "updated_at") VALUES (2, 1, 'message_data_table_name', 'config_1', '2025-07-19 11:16:00', '2025-07-19 11:16:00');
INSERT INTO "main"."config_unique_constraints" ("id", "config_file_record_id", "config_key", "config_value", "created_at", "updated_at") VALUES (3, 1, 'app_name', '服务端配置_2', '2025-07-19 11:16:00', '2025-07-19 11:16:00');
    """
    # 连接数据库
    conn = sqlite3.connect(constants.CROSS_PROCESS_DATA_BASE_PATH)
    cursor = conn.cursor()
    # 执行插入语句
    cursor.executescript(insert_sql)
    # 提交事务
    conn.commit()
    # 关闭数据库连接
    conn.close()


def get_config_files(include_subdirs=False):
    """获取配置文件列表
    
    Args:
        include_subdirs: 是否包含子目录中的文件
    
    Returns:
        list: 配置文件路径列表
    """
    error_dir = BASE_PATH / "tests" / "other_conf" / "error"
    
    if not error_dir.exists():
        print(f"错误：目录不存在 {error_dir}")
        return []
    
    config_files = sorted([f for f in error_dir.iterdir() if f.is_file()])
    
    if include_subdirs:
        miss_config_item_dir = error_dir / "miss_config_item"
        if miss_config_item_dir.exists():
            miss_config_files = [f for f in miss_config_item_dir.iterdir() if f.is_file()]
            config_files.extend(miss_config_files)
    
    return config_files


def print_test_results(results, config_files):
    """打印测试结果
    
    Args:
        results: 测试结果列表
        config_files: 配置文件列表
    """
    no_exception_files = [file_name for file_name, has_exception, _ in results if not has_exception]
    
    print("配置文件错误测试")
    print("=" * 50)
    print(f"找到 {len(config_files)} 个配置文件\n")
    
    # 循环结束后统一打印结果
    print("测试过程详情:")
    for i, (file_name, has_exception, error_msg) in enumerate(results, 1):
        print(f"{i:2d}. 测试 {file_name}")
        # 显示结果
        if has_exception:
            print(f"    结果: ❌ 抛出异常")
            if error_msg:
                # webhook_server_test显示200个字符，server_config_gui_test显示100个字符
                # 这里统一使用150个字符作为折中
                print(f"    错误: {error_msg[:150]}...")
        else:
            print(f"    结果: ⚠️  未抛出异常")
        print()
    
    # 汇总结果
    print("=" * 50)
    print("测试结果汇总:")
    print(f"总文件数: {len(config_files)}")
    print(f"抛出异常: {len(config_files) - len(no_exception_files)} 个")
    print(f"未抛出异常: {len(no_exception_files)} 个")
    
    if no_exception_files:
        print(f"\n🔍 重点关注 - 未抛出异常的文件:")
        for file_name in no_exception_files:
            print(f"   ⚠️  {file_name}")
    
    print("\n详细结果:")
    for file_name, has_exception, error_msg in results:
        status = "❌ 异常" if has_exception else "⚠️  正常"
        print(f"   {file_name:<30} {status}")


def run_test_with_config_test_function(config_test_func, include_subdirs=False):
    """运行测试的通用函数
    
    Args:
        config_test_func: 配置文件测试函数
        include_subdirs: 是否包含子目录中的文件
    """
    # 前置操作：清空数据库
    clean_database()
    # 前置操作：插入预先需要的数据
    insert_pre_need_data()
    
    # 获取配置文件
    config_files = get_config_files(include_subdirs)
    if not config_files:
        return
    
    results = []
    
    for config_file in config_files:
        file_name = config_file.name
        if file_name == "second_modify_run_error.ini":
            continue
        
        has_exception, error_msg = config_test_func(str(config_file))
        results.append((file_name, has_exception, error_msg))
    
    shutdown_exec_funct.execute_registered_functions()
    print_test_results(results, config_files)
