#!/usr/bin/env python3
"""
Linux平台PyInstaller打包脚本，支持生成deb包
"""

import os
import shutil
import subprocess
import sys
import tempfile
from pathlib import Path

from build_common import (
    SOFTWARE_NAME, PROJECT_ROOT, DIST_DIR, main_build_flow,
    create_release_dir, copy_tree_with_info, print_success_message, print_failure_message
)


def handle_linux_build_output():
    """
    处理Linux构建输出
    :return: 是否成功
    """
    # 检查输出
    app_path = DIST_DIR / SOFTWARE_NAME

    if not app_path.exists():
        print("错误: 未找到输出文件")
        return False

    print(f"✓ 打包成功: {app_path}")

    # 创建发布目录
    release_dir = create_release_dir( "linux")

    # 复制应用文件
    release_app = release_dir / SOFTWARE_NAME
    copy_tree_with_info(app_path, release_app, "发布文件")

    # 创建启动脚本
    create_launcher_script(release_dir)

    # 创建deb包
    create_deb_package( release_dir)

    return True


def main():
    """Linux打包主函数"""
    return main_build_flow(
        platform_name="Linux",
        additional_cleanup=None,
        post_build_handler=handle_linux_build_output
    )

def create_launcher_script(release_dir):
    """创建启动脚本"""
    launcher_script = release_dir / "start.sh"
    script_content = f'''#!/bin/bash

echo "启动NexusRecv WebHook Server GUI..."
echo ""
echo "如果遇到问题，请检查："
echo "1. 显示服务器设置 (DISPLAY环境变量)"
echo "2. 防火墙设置"
echo "3. 端口占用情况"
echo "4. 配置文件权限"
echo ""

# 设置DISPLAY环境变量（如果未设置）
if [ -z "$DISPLAY" ]; then
    export DISPLAY=:0.0
fi

# 启动应用
cd "$(dirname "$0")"
./{SOFTWARE_NAME}/{SOFTWARE_NAME}

echo ""
echo "按任意键退出..."
read -n 1
'''

    with open(launcher_script, 'w', encoding='utf-8') as f:
        f.write(script_content)

    # 设置执行权限
    os.chmod(launcher_script, 0o755)
    print(f"✓ 启动脚本: {launcher_script}")

def create_deb_package(release_dir):
    """创建deb包"""
    print("创建deb包...")

    # 检查dpkg-deb是否可用
    try:
        subprocess.run(["dpkg-deb", "--version"],
                      capture_output=True, check=True)
    except (subprocess.CalledProcessError, FileNotFoundError):
        print("警告：dpkg-deb不可用，跳过deb包创建")
        print("要创建deb包，请安装: sudo apt-get install dpkg-dev")
        return

    # 创建临时目录结构
    with tempfile.TemporaryDirectory() as temp_dir:
        temp_path = Path(temp_dir)

        # 创建deb包目录结构
        deb_root = temp_path / "nexusrecv-webhook-server_1.0.0_amd64"
        deb_root.mkdir()

        # DEBIAN控制目录
        debian_dir = deb_root / "DEBIAN"
        debian_dir.mkdir()

        # 应用程序目录
        app_dir = deb_root / "opt" / "nexusrecv-webhook-server"
        app_dir.mkdir(parents=True)

        # 桌面文件目录
        desktop_dir = deb_root / "usr" / "share" / "applications"
        desktop_dir.mkdir(parents=True)

        # 复制应用文件
        shutil.copytree(release_dir / SOFTWARE_NAME,
                       app_dir / SOFTWARE_NAME)

        # 创建控制文件
        create_control_file(debian_dir)

        # 创建桌面文件
        create_desktop_file(desktop_dir)

        # 创建启动脚本
        create_system_launcher(deb_root)

        # 构建deb包
        deb_output = PROJECT_ROOT / "release" / "nexusrecv-webhook-server_1.0.0_amd64.deb"
        cmd = ["dpkg-deb", "--build", str(deb_root), str(deb_output)]

        result = subprocess.run(cmd, capture_output=True, text=True)
        if result.returncode == 0:
            print(f"✓ deb包创建成功: {deb_output}")
            print(f"安装命令: sudo dpkg -i {deb_output.name}")
        else:
            print(f"deb包创建失败: {result.stderr}")

def create_control_file(debian_dir):
    """创建DEBIAN/control文件"""
    control_content = '''Package: nexusrecv-webhook-server
Version: 1.0.0
Section: net
Priority: optional
Architecture: amd64
Depends: libc6, libgcc-s1, libstdc++6
Maintainer: NexusRecv Team <<EMAIL>>
Description: NexusRecv WebHook Server GUI Application
 A GUI application for managing webhook server configurations
 and monitoring webhook message reception.
 .
 Features:
  - Configuration management
  - Real-time message monitoring
  - Multi-process support
  - Cross-platform compatibility
'''

    control_file = debian_dir / "control"
    with open(control_file, 'w', encoding='utf-8') as f:
        f.write(control_content)

def create_desktop_file(desktop_dir):
    """创建桌面文件"""
    desktop_content = f'''[Desktop Entry]
Version=1.0
Type=Application
Name=NexusRecv WebHook Server
Comment=WebHook Server Configuration and Monitoring
Exec=/opt/nexusrecv-webhook-server/nexusrecv-webhook-server
Icon=/opt/nexusrecv-webhook-server/resources/22.png
Terminal=false
Categories=Network;Development;
StartupNotify=true
'''

    desktop_file = desktop_dir / "nexusrecv-webhook-server.desktop"
    with open(desktop_file, 'w', encoding='utf-8') as f:
        f.write(desktop_content)

def create_system_launcher(deb_root):
    """创建系统级启动脚本"""
    bin_dir = deb_root / "usr" / "local" / "bin"
    bin_dir.mkdir(parents=True)

    launcher_content = f'''#!/bin/bash
cd /opt/nexusrecv-webhook-server
exec ./{SOFTWARE_NAME}/{SOFTWARE_NAME} "$@"
'''

    launcher_file = bin_dir / "nexusrecv-webhook-server"
    with open(launcher_file, 'w', encoding='utf-8') as f:
        f.write(launcher_content)

    # 设置执行权限
    os.chmod(launcher_file, 0o755)

if __name__ == "__main__":
    success = main()
    if success:
        print_success_message("Linux")
        sys.exit(0)
    else:
        print_failure_message("Linux")
        sys.exit(1)
