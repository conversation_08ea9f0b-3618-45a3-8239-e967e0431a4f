"""滑块验证码识别器模块。

使用Playwright和ddddocr API实现滑块拼图验证码的自动识别和操作。
"""

import asyncio
import logging
from abc import ABC, abstractmethod
from typing import Optional, Tuple

from playwright.async_api import async_playwright, <PERSON>, Browser, BrowserContext

from common.utils import logging_utils
from .ddddocr_client import DDDDOCRClient

logger = logging.getLogger(__name__)


class SliderCaptchaRecognizer(ABC):
    """滑块验证码识别器基类。

    使用Playwright控制浏览器，结合ddddocr API实现滑块验证码的自动识别和操作。
    具体的图片获取逻辑需要在子类中实现。
    """
    
    def __init__(self,
                 ddddocr_base_url: str = "http://10.168.1.201:7777",
                 headless: bool = True,
                 browser_type: str = "chromium"):
        """初始化滑块验证码识别器。

        Args:
            ddddocr_base_url: ddddocr服务的基础URL
            headless: 是否使用无头模式运行浏览器
            browser_type: 浏览器类型 ("chromium", "firefox", "webkit")
        """
        self.ddddocr_client = DDDDOCRClient(ddddocr_base_url)
        self.headless = headless
        self.browser_type = browser_type
        
        self.playwright = None
        self.browser: Optional[Browser] = None
        self.context: Optional[BrowserContext] = None
        self.page: Optional[Page] = None
        
        logging_utils.logger_print(
            msg=f"initialized slider captcha recognizer with browser_type: {browser_type}, headless: {headless}",
            custom_logger=logger
        )
    
    async def __aenter__(self):
        """异步上下文管理器入口。"""
        await self.start()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口。"""
        await self.close()
    
    async def start(self):
        """启动浏览器和页面。"""
        logging_utils.logger_print(
            msg="starting playwright browser",
            custom_logger=logger
        )
        
        self.playwright = await async_playwright().start()
        
        # 选择浏览器类型
        if self.browser_type == "firefox":
            browser_launcher = self.playwright.firefox
        elif self.browser_type == "webkit":
            browser_launcher = self.playwright.webkit
        else:
            browser_launcher = self.playwright.chromium
        
        # 启动浏览器
        self.browser = await browser_launcher.launch(
            headless=self.headless,
            args=[
                '--no-sandbox',
                '--disable-blink-features=AutomationControlled',
                '--disable-web-security',
                '--disable-features=VizDisplayCompositor'
            ]
        )
        
        # 创建浏览器上下文
        self.context = await self.browser.new_context(
            viewport={'width': 1920, 'height': 1080},
            user_agent='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
        )
        
        # 创建页面
        self.page = await self.context.new_page()
        
        logging_utils.logger_print(
            msg="playwright browser started successfully",
            custom_logger=logger
        )
    
    async def close(self):
        """关闭浏览器和相关资源。"""
        logging_utils.logger_print(
            msg="closing playwright browser",
            custom_logger=logger
        )
        
        if self.page:
            await self.page.close()
            self.page = None
        
        if self.context:
            await self.context.close()
            self.context = None
        
        if self.browser:
            await self.browser.close()
            self.browser = None
        
        if self.playwright:
            await self.playwright.stop()
            self.playwright = None
        
        if self.ddddocr_client:
            self.ddddocr_client.close()
        
        logging_utils.logger_print(
            msg="playwright browser closed",
            custom_logger=logger
        )
    
    async def navigate_to_page(self, url: str, wait_timeout: int = 30000):
        """在指定页面打开URL所对应的网页。
        
        Args:
            url: 目标页面URL
            wait_timeout: 等待超时时间（毫秒）
        """
        if not self.page:
            raise RuntimeError("browser not started, call start() first")
        
        logging_utils.logger_print(
            msg=f"navigating to page: {url}",
            custom_logger=logger
        )
        
        await self.page.goto(url, wait_until='domcontentloaded', timeout=wait_timeout)
        
        # 等待页面稳定
        await self.page.wait_for_timeout(2000)
    
    async def capture_element_screenshot(self, selector: str) -> bytes:
        """截取指定标签页中指定元素的截图。
        
        Args:
            selector: 元素选择器
            
        Returns:
            截图的二进制数据
        """
        if not self.page:
            raise RuntimeError("browser not started, call start() first")
        
        element = await self.page.wait_for_selector(selector, timeout=10000)
        if not element:
            raise RuntimeError(f"element not found: {selector}")
        
        screenshot_data = await element.screenshot()
        
        logging_utils.logger_print(
            msg=f"captured screenshot for element: {selector}",
            custom_logger=logger
        )
        
        return screenshot_data
    
    @abstractmethod
    async def get_slider_images(self, *args, **kwargs) -> Tuple[bytes, bytes]:
        """获取滑块验证码的背景图和滑块图。
        该方法需要在子类中实现，不同的网站获取的方式存在区别。

        Args:
            *args: 可变位置参数，具体参数根据不同网站的实现而定
            **kwargs: 可变关键字参数，具体参数根据不同网站的实现而定

        Returns:
            (滑块图数据, 背景图数据)：两个 bytes 对象，分别表示滑块图片和背景图片
        """
        pass

    async def wait_for_captcha(self,
                              captcha_selectors: Optional[list] = None,
                              timeout: int = 30000) -> bool:
        """等待验证码出现。
        通用的验证码等待方法，子类可以重写以实现特定的等待逻辑。

        Args:
            captcha_selectors: 验证码元素选择器列表，如果为None则使用默认选择器
            timeout: 等待超时时间（毫秒）

        Returns:
            是否成功检测到验证码
        """
        if not self.page:
            raise RuntimeError("browser not started, call start() first")

        # 默认的验证码选择器
        if captcha_selectors is None:
            captcha_selectors = [
                '.captcha', '.verification', '.verify',
                '[class*="captcha"]', '[class*="verify"]'
            ]

        logging_utils.logger_print(
            msg=f"waiting for captcha with selectors: {captcha_selectors}",
            custom_logger=logger
        )

        try:
            # 尝试等待任一验证码元素出现
            selector_string = ', '.join(captcha_selectors)
            await self.page.wait_for_selector(selector_string, timeout=timeout)

            logging_utils.logger_print(
                msg="captcha detected successfully",
                custom_logger=logger
            )
            return True

        except Exception as e:
            logging_utils.logger_print(
                msg="failed to detect captcha",
                custom_logger=logger,
                use_exception=True,
                exception=e
            )
            return False
    
    async def solve_slider_captcha(self,
                                  slider_data: bytes,
                                  background_data: bytes,
                                  background_selector: str,
                                  slider_button_selector: str,
                                  slider_button_x_offset: int = 0,
                                  use_slide_comparison: bool = False) -> bool:
        """解决滑块验证码。
        结合 tencent_slider_solver 中的 solve_captcha 逻辑实现。

        Args:
            slider_data: 滑块图片数据（bytes）
            background_data: 背景图片数据（bytes）
            background_selector: 背景图元素选择器（用于拖拽操作）
            slider_button_selector: 滑块按钮选择器（用于拖拽操作）
            slider_button_x_offset: 滑块和滑块按钮x坐标的差值
            use_slide_comparison: 是否使用slideComparison API（否则使用capcode API，推荐用于腾讯验证码）

        Returns:
            是否成功解决验证码
        """
        if not self.page:
            raise RuntimeError("browser not started, call start() first")

        logging_utils.logger_print(
            msg="starting to solve slider captcha with image bytes",
            custom_logger=logger
        )

        try:
            # 使用ddddocr API识别滑块位置
            if use_slide_comparison and slider_data:
                # 使用slideComparison API
                target_x = self.ddddocr_client.slide_comparison(
                    slider_data, background_data
                )
                logging_utils.logger_print(
                    msg=f"used slideComparison API, target_x: {target_x}",
                    custom_logger=logger
                )
            elif slider_data:
                # 使用capcode API（推荐用于腾讯验证码）
                target_x = self.ddddocr_client.capcode(
                    slider_data, background_data
                )
                logging_utils.logger_print(
                    msg=f"used capcode API, target_x: {target_x}",
                    custom_logger=logger
                )
            else:
                # 如果没有滑块图，使用背景图进行识别
                target_x = self.ddddocr_client.capcode(background_data, background_data)
                logging_utils.logger_print(
                    msg=f"used capcode API with background only, target_x: {target_x}",
                    custom_logger=logger
                )

            logging_utils.logger_print(
                msg=f"detected target position: x={target_x}",
                custom_logger=logger
            )

            # 执行拖拽
            success = await self._drag_slider(
                target_x,
                background_selector,
                slider_button_selector,
                slider_button_x_offset
            )

            if success:
                logging_utils.logger_print(
                    msg="slider captcha solved successfully",
                    custom_logger=logger
                )
            else:
                logging_utils.logger_print(
                    msg="failed to solve slider captcha",
                    custom_logger=logger
                )

            return success

        except Exception as e:
            logging_utils.logger_print(
                msg="error solving slider captcha",
                custom_logger=logger,
                use_exception=True,
                exception=e
            )
            return False

    async def _drag_slider(self,
                          target_x: int,
                          background_selector: str,
                          slider_button_selector: str,
                          slider_button_x_offset: int = 0) -> bool:
        """拖拽滑块到目标位置。
        参考 tencent_slider_solver 中的 _drag_slider 逻辑实现。

        Args:
            target_x: ddddocr返回的目标位置绝对x坐标（相对于背景图）
            background_selector: 背景图元素选择器
            slider_button_selector: 滑块按钮选择器
            slider_button_x_offset: 滑块和滑块按钮x坐标的差值

        Returns:
            是否拖拽成功
        """
        try:
            logging_utils.logger_print(
                msg=f"starting drag operation for target_x: {target_x}",
                custom_logger=logger
            )

            # 清除焦点，避免键盘/全选等干扰
            try:
                await self.page.evaluate("() => { if (document.activeElement) document.activeElement.blur(); }")
                logging_utils.logger_print(msg="cleared focus from active element", custom_logger=logger)
            except Exception as blur_error:
                logging_utils.logger_print(msg=f"failed to clear focus: {blur_error}", custom_logger=logger)

            # 获取滑块按钮和背景图元素
            slider_button = await self.page.query_selector(slider_button_selector)
            if not slider_button:
                logging_utils.logger_print(msg="slider button not found", custom_logger=logger)
                return False

            # 查找背景图元素
            bg_element = await self.page.query_selector(background_selector)
            if not bg_element:
                logging_utils.logger_print(msg="background element not found", custom_logger=logger)
                return False

            # 获取滑块按钮和背景图的位置信息
            button_box = await slider_button.bounding_box()
            bg_box = await bg_element.bounding_box()

            if not button_box or not bg_box:
                logging_utils.logger_print(msg="failed to get element bounding boxes", custom_logger=logger)
                return False

            logging_utils.logger_print(msg=f"button_box: {button_box}", custom_logger=logger)
            logging_utils.logger_print(msg=f"bg_box: {bg_box}", custom_logger=logger)

            # 计算滑块当前在背景图中的相对位置
            # 考虑滑块和滑块按钮的x坐标差值
            slider_left_edge_x = button_box['x'] + slider_button_x_offset
            slider_current_x_in_bg = slider_left_edge_x - bg_box['x']

            # 同时计算滑块中心位置用于对比
            slider_center_x = button_box['x'] + button_box['width'] / 2
            slider_center_x_in_bg = slider_center_x - bg_box['x']

            logging_utils.logger_print(
                msg=f"slider left edge position in background: {slider_current_x_in_bg}px",
                custom_logger=logger
            )
            logging_utils.logger_print(
                msg=f"slider center position in background: {slider_center_x_in_bg}px",
                custom_logger=logger
            )

            # 计算需要移动的距离
            # target_x 是目标位置在背景图中的绝对坐标（通常是滑块左边缘应该到达的位置）
            # slider_current_x_in_bg 是滑块当前左边缘在背景图中的位置
            move_distance = target_x - slider_current_x_in_bg

            logging_utils.logger_print(
                msg=f"calculated move distance: {move_distance}px (target: {target_x}, current left edge: {slider_current_x_in_bg})",
                custom_logger=logger
            )

            # 计算滑块中心（基于 button_box）
            start_x = button_box['x'] + button_box['width'] / 2
            start_y = button_box['y'] + button_box['height'] / 2

            logging_utils.logger_print(
                msg=f"final start coords: ({start_x}, {start_y}), move_distance: {move_distance}",
                custom_logger=logger
            )

            end_y = start_y

            # 限制移动步数
            steps = max(10, int(abs(move_distance) // 5))
            logging_utils.logger_print(msg=f"dragging in {steps} steps", custom_logger=logger)

            # 选择正确的页面进行鼠标操作
            # 如果当前页面没有 mouse 属性（如 Frame 对象），使用 main_page
            mouse_page = self.page
            if not hasattr(self.page, 'mouse') and hasattr(self, 'main_page') and self.main_page:
                mouse_page = self.main_page
                logging_utils.logger_print(msg="using main_page for mouse operations", custom_logger=logger)

            # 移动鼠标到起点并按下
            logging_utils.logger_print(msg="moving mouse to start position", custom_logger=logger)
            await mouse_page.mouse.move(start_x, start_y)
            await mouse_page.mouse.down()

            # 平滑分步移动到目标（可加入微小随机偏移以模拟人为操作）
            import random
            for i in range(1, steps + 1):
                progress = i / steps
                current_x = start_x + move_distance * progress
                jitter = random.uniform(-1.0, 1.0)
                await mouse_page.mouse.move(current_x + jitter, end_y)
                await mouse_page.mouse.move(current_x, end_y)
                await asyncio.sleep(0.015)

            await mouse_page.mouse.up()
            logging_utils.logger_print(msg="drag operation completed", custom_logger=logger)

            # 等待一点时间观察结果
            await self.page.wait_for_timeout(1200)
            return True

        except Exception as e:
            logging_utils.logger_print(
                msg="error dragging slider",
                custom_logger=logger,
                use_exception=True,
                exception=e
            )
            return False
    
    async def wait_for_captcha_result(self, 
                                     success_selector: Optional[str] = None,
                                     failure_selector: Optional[str] = None,
                                     timeout: int = 10000) -> Optional[bool]:
        """等待验证码验证结果。
        
        Args:
            success_selector: 成功标识元素选择器
            failure_selector: 失败标识元素选择器
            timeout: 等待超时时间（毫秒）
            
        Returns:
            验证结果：True表示成功，False表示失败，None表示超时
        """
        if not self.page:
            return None
        
        try:
            if success_selector and failure_selector:
                # 等待成功或失败元素出现
                result = await self.page.wait_for_selector(
                    f"{success_selector}, {failure_selector}",
                    timeout=timeout
                )
                
                if result:
                    # 检查是哪个元素
                    if await result.evaluate(f"el => el.matches('{success_selector}')"):
                        return True
                    else:
                        return False
            elif success_selector:
                # 只等待成功元素
                result = await self.page.wait_for_selector(success_selector, timeout=timeout)
                return result is not None
            elif failure_selector:
                # 只等待失败元素
                result = await self.page.wait_for_selector(failure_selector, timeout=timeout)
                return result is None  # 如果失败元素出现，返回False
            
        except Exception as e:
            logging_utils.logger_print(
                msg="timeout waiting for captcha result",
                custom_logger=logger
            )
        
        return None
