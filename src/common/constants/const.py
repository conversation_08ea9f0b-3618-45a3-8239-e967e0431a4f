import os
import platform
import re
import sys
from pathlib import Path
from zoneinfo import ZoneInfo

# ====================== 项目路径相关常量 ======================
# 当前项目的根目录
BASE_PATH=Path(sys._MEIPASS) if getattr(sys, 'frozen', False) else Path(__file__).parent.parent.parent.parent# noqa
# 当前项目的src目录
SRC_PATH=BASE_PATH/"src"
SRC_PATH_STR = str(SRC_PATH)

# 确保源代码路径在sys.path中,且在第一位
if SRC_PATH_STR in sys.path:
    sys.path.remove(SRC_PATH_STR)
sys.path.insert(0,SRC_PATH_STR)

def get_resource_path(relative_path: str) -> str:
    """
    获取该项目内文件的绝对路径，支持开发环境和打包环境。
    由于该函数需要在本文件中使用，那么就不能依赖本项目文件,只能在本文件中定义该函数来使用

    Args:
        relative_path: 相对路径

    Returns:
        str: 项目内文件的绝对路径
    """
    return  str(BASE_PATH/relative_path)
# ====================== 操作系统相关常量 ======================
# 当前操作系统类型
CURRENT_OS_NAME = os.name  # 'nt' for Windows, 'posix' for Unix-like
CURRENT_PLATFORM = sys.platform  # 'win32', 'linux', 'darwin', etc.
CURRENT_SYSTEM = platform.system()  # 'Windows', 'Linux', 'Darwin', etc.

# 操作系统判断常量
IS_WINDOWS = (sys.platform == 'win32')
IS_LINUX = (sys.platform == 'linux')
IS_MACOS = (sys.platform == 'darwin')
IS_UNIX_LIKE = (os.name == 'posix')

# 平台特定常量
PLATFORM_WINDOWS = "win32"
PLATFORM_LINUX = "linux"
PLATFORM_MACOS = "darwin"
OS_NAME_POSIX = "posix"
SYSTEM_WINDOWS = "Windows"
SYSTEM_LINUX = "Linux"
SYSTEM_MACOS = "Darwin"

# -----------------------------------------------
TIME_ZONE = "Asia/Shanghai"
# 默认时区
DEFAULT_TIMEZONE = ZoneInfo(TIME_ZONE)
UTC_ZONE = ZoneInfo("UTC")
# WAL文件进行检查的最小大小（MB）
MAX_WAL_SIZE_MB = 5
# 年月日-时分秒格式化字符串
DATETIME_FORMAT = "%Y-%m-%d %H:%M:%S"

# STORAGE_UNIT中对应的英文单位的最大长度: 目前最长的是 BYTES
STORAGE_UNIT_KEY_MAX_LEN = 5
# 数据存储单位的正则表达式: 1GB/1.1MB/1024KB/1024B
STORAGE_UNIT_PATTERN = re.compile(
    fr"^([0-9]+(?:\.[0-9]+)?)([A-Za-z]{{0,{STORAGE_UNIT_KEY_MAX_LEN}}})$"
)
# 对应单位转换成字节的映射关系:不带单位则默认是字节单位
STORAGE_UNIT = {
    '': 1,
    'B': 1,
    'K': 1024,
    'M': 1024 ** 2,
    'G': 1024 ** 3,
    'T': 1024 ** 4,
    'P': 1024 ** 5,
    'E': 1024 ** 6,
    'KB': 1024,
    'MB': 1024 ** 2,
    'GB': 1024 ** 3,
    'TB': 1024 ** 4,
    'PB': 1024 ** 5,
    'EB': 1024 ** 6,
    'KIB': 1024,
    'MIB': 1024 ** 2,
    'GIB': 1024 ** 3,
    'TIB': 1024 ** 4,
    'PIB': 1024 ** 5,
    'EIB': 1024 ** 6,
    'BYTE': 1,
    'BYTES': 1
}
# http端口范围
MIN_PORT = 0
MAX_PORT = 65535

# ====================== logging 相关常量 ======================
LOG_LEVELS = ["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"]
# 以下是self_log的默认配置
LOG_LEVEL = "ERROR"
LOG_CONSOLE = True
LOG_FILE = True
LOG_DIR = os.path.join(os.path.expanduser("~"), ".logs")
LOG_FILE_NAME_PREFIX = "common_"
LOG_FILE_DATE_FMT = "%Y-%m-%d"
LOG_FILENAME = "%(filename_prefix)s-%(levelname)s-%(filename_date_fmt)s.log"
LOG_FILE_MAX_SIZE = "10MB"
LOG_BACKUP_COUNT = 3
LOG_FORMAT = "%(asctime)s [%(levelname)s] [PID:%(process)d] %(message)s"
LOG_DATE_FMT = DATETIME_FORMAT
# 日志保留天数[其删除也只会删除指定filename_prefix前缀的日志文件]
LOG_EXPIRE_LOGS_DAYS = 5
LOG_ZONE = TIME_ZONE
# 日志颜色配置
LOG_COLOR_DEBUG = "LIGHTBLACK_EX"
LOG_COLOR_INFO = "LIGHTWHITE_EX"
LOG_COLOR_WARNING = "YELLOW"
LOG_COLOR_ERROR = "RED"
LOG_COLOR_CRITICAL = "BRIGHT+WHITE"
# 在logger没有生效的情况下print输出的临时日志文件路径
TEMP_LOG_FILE_PATH = os.path.join(
    os.path.expanduser("~"), ".logs", "temp_log_"
)
