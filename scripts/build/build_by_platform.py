#!/usr/bin/env python3
"""
跨平台PyInstaller打包脚本
根据当前平台自动选择合适的打包方式
"""

import sys
from pathlib import Path

# 添加src路径以便导入gui_constants
sys.path.insert(0, str(Path(__file__).parent.parent.parent / "src"))
from config import gui_constants


def main():
    """主函数，根据平台选择打包方式"""
    current_platform = gui_constants.CURRENT_PLATFORM

    print("=" * 50)
    print("NexusRecv 跨平台打包工具")
    print("=" * 50)
    print(f"检测到当前平台: {current_platform}")
    try:
        if gui_constants.IS_WINDOWS:
            print("使用Windows打包脚本...")
            import build_windows
            build_res = build_windows.main()

        elif gui_constants.IS_LINUX:
            print("使用Linux打包脚本...")
            import build_linux
            build_res = build_linux.main()

        elif gui_constants.IS_MACOS:
            print("macOS平台暂不支持，请使用Linux打包脚本...")
            print("您可以手动运行: python scripts/build/build_linux.py")
            build_res = False

        else:
            print(f"不支持的平台: {current_platform}")
            print("支持的平台: Windows, Linux")
            build_res = False

        print("=" * 50)
        if build_res:
            print("打包完成！")
            return True
        else:
            print("打包失败！")
            return False

    except Exception as e:
        print(f"打包过程中发生错误: {e}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
