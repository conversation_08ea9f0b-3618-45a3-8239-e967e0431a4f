"""状态监控组件。

负责显示服务端状态信息 --- 服务端初始化时间、服务端总运行时长、服务端此刻运行状态、服务端进程ID、服务端[CPU、内存]占用。

服务端启动之后状态监控
"""
import logging
import threading
from typing import Optional

import ttkbootstrap as ttkb
from ttkbootstrap.constants import *

from common.models import gui_widgets
from common.utils import ttkb_gui_utils, process_monitor, logging_utils, time_utils, process_utils
from webhook_server.config import gui_constants
from webhook_server.models import gui_properties, gui_server_info


class StatusMonitorComponent:
    """服务端状态监控组件类"""

    def __init__(self, props: gui_properties.GUIProperties,content_frame:ttkb.Frame):
        """初始化服务端状态显示组件。

        Args:
            props: GUI属性
            content_frame: 主界面中主要内容显示区域 --- 当前组件的父容器
        """
        self.props = props
        self.content_frame = content_frame
        # 服务端状态信息
        # 服务端总运行时间
        self.total_runtime_seconds = 0
        # 服务端点击启动按钮的时间 而非第一次初始化启动的时间 time.monotonic()
        self.server_start_time:Optional[float] = None
        self.status_label: Optional[ttkb.Label] = None
        # 显示第一次启动的时间
        self.server_initial_startup_time:Optional[ttkb.Label] = None
        self.cpu_meter: Optional[gui_widgets.PrecisionMeter] = None # 主界面上显示CPU使用率的组件
        self.gui_server_info:Optional[gui_server_info.GUIServerInfo] = None # 服务端运行时需要显示的信息:服务端运行状态信息、服务端数据显示信息
        # gui界面中按钮手动停止后台更新服务端状态信息线程
        self.stop_server_event = threading.Event()

    def create_widgets(self):
        """创建服务端状态信息显示组件：
        在 实时数据显示区域 下面显示服务端状态信息，如果有必要，也可以补充其他信息：
            1. self.status_label 服务端允许状态
            2. 在程序打开之后启动服务端运行总时长 【实时刷新】
            3. 系统监控信息：进程PID、内存使用、CPU使用率 【实时刷新】
        """
        server_running_frame=ttkb.Frame(self.content_frame)
        server_running_frame.pack(fill=X, expand=False)
        # 服务端状态标签:其由于会涉及到颜色的设置,不方便使用StringVar;
        server_status_font = (self.props.ui_font_family,12)
        server_running_font = (self.props.ui_font_family,10)

        # 服务端运行时文字监控信息
        status_running_text_info_frame = ttkb.Frame(server_running_frame)
        status_running_text_info_frame.pack(side=LEFT, fill=X, expand=True, anchor=N)
        # 创建标签 - 移除width参数，使用固定像素宽度
        self.status_label = ttkb.Label(status_running_text_info_frame,text="服务端未运行",font=server_status_font)
        monitor_pid_label = ttkb.Label(status_running_text_info_frame,textvariable=self.props.server_pid_var,font=server_running_font)
        monitor_memory_label = ttkb.Label(status_running_text_info_frame,textvariable=self.props.server_memory_var,font=server_running_font)
        self.server_initial_startup_time=ttkb.Label(status_running_text_info_frame,text=gui_constants.SERVER_INITIAL_STARTUP_TIME_DEFAULT_VAR,font=server_status_font)
        runtime_label = ttkb.Label(status_running_text_info_frame,textvariable=self.props.server_total_uptime_var,font=server_status_font)


        rows = [
            [self.status_label, monitor_pid_label, monitor_memory_label],
            [self.server_initial_startup_time, runtime_label]
        ]
        for r, row in enumerate(rows):
            for c, widget in enumerate(row):
                if widget:
                    widget.grid(row=r, column=c, sticky=W, padx=2, pady=0)
        for i in range(2):  # 2行
            status_running_text_info_frame.grid_rowconfigure(i, weight=0)
        # 使用固定像素宽度，避免字体变化导致布局问题
        status_running_text_info_frame.grid_columnconfigure(0, weight=0, minsize=315)  # 状态/启动时间列，固定315px
        status_running_text_info_frame.grid_columnconfigure(1, weight=0, minsize=170)  # PID/运行时长列，固定170px
        status_running_text_info_frame.grid_columnconfigure(2, weight=0, minsize=90)  # 内存列，固定90px

        # 服务端运行时视图监控信息
        server_running_view_info_frame = ttkb.Frame(server_running_frame)
        server_running_view_info_frame.pack(side=LEFT, fill=X, expand=True, anchor=N)
        self.cpu_meter = ttkb_gui_utils.create_cpu_meter(server_running_view_info_frame, 130, 8)
        self.cpu_meter.pack(side=TOP,anchor=NW,padx=(0,0),pady=5)

    def start_server_process_monitor(self):
        """启动服务端进程监控器"""
        try:
            # 停止之前的服务端监控器（如果存在）
            self.stop_server_process_monitor()

            if self.props.server_process and self.props.server_process.poll() is None:
                server_pid = self.props.server_process.pid
                self.props.logger.info(f"creating server process monitor for pid: {server_pid}")

                # 创建专门监控服务端进程的监控器实例
                self.props.server_monitor = process_monitor.ProcessMonitor(interval=3, pid=server_pid)
                self.props.server_monitor.start()

                self.props.logger.info(f"server process monitor started successfully for pid: {server_pid}")
            else:
                self.props.logger.warning("cannot start server process monitor: server process not running")
        except Exception as e:
            self.props.logger.error(f"failed to start server process monitor: {e}")
            self.props.server_monitor = None

    def stop_server_process_monitor(self):
        """停止服务端进程监控器"""
        try:
            if hasattr(self.props, 'server_monitor') and self.props.server_monitor is not None:
                self.props.logger.info("stopping server process monitor")
                self.props.server_monitor.stop()
                self.props.server_monitor = None
                self.props.logger.info("server process monitor stopped successfully")
        except Exception as e:
            self.props.logger.error(f"failed to stop server process monitor: {e}")
            self.props.server_monitor = None

    def reset_system_monitor_info(self):
        """重置系统监控信息显示"""
        try:
            logging_utils.logger_print(msg="resetting system monitor info", custom_logger=self.props.logger, log_level=logging.DEBUG)
            if hasattr(self.props, 'server_pid_var'):
                self.props.server_pid_var.set(gui_constants.SERVER_PID_DEFAULT_VAR)
            if hasattr(self.props, 'server_memory_var'):
                self.props.server_memory_var.set(gui_constants.SERVER_MEMORY_DEFAULT_VAR)
            if hasattr(self, 'cpu_meter'):
                ttkb_gui_utils.update_cpu_meter(self.cpu_meter, 0.0, gui_constants.CPU_THRESHOLD_SEGMENT_STYLE)
            logging_utils.logger_print(msg="system monitor info reset completed", custom_logger=self.props.logger, log_level=logging.DEBUG)
        except Exception as e:
            self.props.show_operation_error(error=e, error_msg="重置系统监控信息失败",parent_gui=self.props.root)

    def refresh_ui(self, current_gui_server_info:gui_server_info.GUIServerInfo):
        """只有在服务端启动成功和后台监控线程启动成功后，才会调用该方法刷新服务端状态信息显示UI"""
        self.status_label.config(text=current_gui_server_info.server_status_message, foreground=current_gui_server_info.server_status_color)
        self.total_runtime_seconds,server_running_duration= time_utils.format_time_monotonic_interval(start_timestamp=self.server_start_time)
        self.props.server_total_uptime_var.set(f"服务端运行总时长:{server_running_duration}")

        # 更新系统监控信息 - 使用服务端进程监控器 server_monitor是在启动服务端时启动的,因此server_monitor属性是一定存在的
        pid, cpu_usage, memory_usage = self.props.server_monitor.get_usage()
        self.props.server_pid_var.set(f"服务端PID: {pid}")
        self.props.server_memory_var.set(f"内存使用: {memory_usage / 1024 /1024:.2f} MB")
        ttkb_gui_utils.update_cpu_meter(self.cpu_meter, cpu_usage, gui_constants.CPU_THRESHOLD_SEGMENT_STYLE)

    def update_server_status(self):
        """在后台线程中更新服务端状态信息"""
        self.props.logger.debug("update server status")
        while not self.stop_server_event.is_set():
            server_process = getattr(self.props, 'server_process', None)
            # 最新服务端状态信息
            cur_gui_server_info=gui_server_info.GUIServerInfo()
            need_break=False
            try:
                if not server_process:
                    cur_gui_server_info.server_status_message="服务端未启动"
                    cur_gui_server_info.server_status_color="black"
                    self.props.server_process = None
                    need_break=True
                elif (exit_code:=server_process.poll()) is not None:
                    cur_gui_server_info.exit_code=exit_code
                    self.props.logger.warning(f"server process (pid: {server_process.pid}) exited with code: {exit_code}")
                    if exit_code !=0:
                        error_msg:str =f"服务端进程异常退出,退出码: ({exit_code})\n请自行查询日志解决问题或者联系开发者"
                        cur_gui_server_info.exit_reason=error_msg
                        cur_gui_server_info.server_status_message="服务端异常停止"
                        cur_gui_server_info.server_status_color="red"
                    else:
                        self.props.logger.info("server process exited normally")
                        cur_gui_server_info.server_status_message="服务端已停止"
                        cur_gui_server_info.server_status_color="black"
                    self.props.logger.info(f"server process exited with code: {exit_code}")
                    process_utils.process_end_output(server_process)
                    cur_gui_server_info.server_button_flag=True
                    # 服务端进程退出时停止监控器
                    self.stop_server_process_monitor()
                    self.props.server_process = None
                    need_break=True
                else:
                    self.props.logger.debug("server process is still alive, scheduling next status check in 5 seconds")
                    cur_gui_server_info.server_status_message="服务端运行中"
                    cur_gui_server_info.server_status_color="green"
                    cur_gui_server_info.server_button_flag=False
                    cur_gui_server_info.new_data=self.props.message_data_manager.get_recent_data(10)
                    cur_gui_server_info.cur_get_new_data=True
                    self.stop_server_event.wait(timeout=0.8)
            except BaseException: # noqa
                self.props.logger.exception("error during server status check!", exc_info=True)
                # 异常时停止监控器
                self.stop_server_process_monitor()
                self.props.server_process = None
                cur_gui_server_info.stop_server_flag=True
                cur_gui_server_info.server_button_flag=True
                cur_gui_server_info.server_status_message="服务端异常停止"
                cur_gui_server_info.server_status_color="red"
                self.stop_server_event.set()
                self.status_label.config(text="服务端异常停止",foreground="red")
            self.gui_server_info=cur_gui_server_info
            if need_break:
                self.stop_server_event.set()
                break
