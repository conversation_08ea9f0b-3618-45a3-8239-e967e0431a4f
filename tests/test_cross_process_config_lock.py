"""
多进程配置项唯一性管理器测试
"""

import logging
import multiprocessing
import os
import sys
import tempfile
import time

import pytest

# 确保能够导入 src 目录下的模块
src_path = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', 'src'))
if src_path not in sys.path:
    sys.path.append(src_path)

from tests.discard.runtime_webhook_server_config_lock import RuntimeWebhookConfigManager

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 进程间通信状态常量
REGISTER_SUCCESS = "REGISTER_SUCCESS"
REGISTER_FAILED = "REGISTER_FAILED"
PROCESS_COMPLETED = "PROCESS_COMPLETED"

# 测试用的临时数据库路径
@pytest.fixture
def temp_db_path():
    """创建临时数据库文件路径"""
    fd, path = tempfile.mkstemp(suffix='.db')
    os.close(fd)
    yield path
    # 清理
    try:
        os.unlink(path)
    except (FileNotFoundError, PermissionError):
        pass

@pytest.fixture
def config_manager(temp_db_path):
    """创建配置管理器实例"""
    # 使用临时数据库路径创建管理器实例
    manager = RuntimeWebhookConfigManager.get_singleton_instance(db_path=temp_db_path)
    yield manager
    # 清理
    manager.cleanup_current_process()

# ====================== 辅助函数 ======================

def register_config_process(db_path, config_key, config_value, queue, hold_time=1):
    """在单独进程中注册配置项"""
    try:
        # 创建管理器实例
        manager = RuntimeWebhookConfigManager.get_singleton_instance(db_path=db_path)
        
        # 尝试注册配置
        success = manager.register_config(config_key, config_value)
        
        if success:
            queue.put(REGISTER_SUCCESS)
            # 持有配置一段时间
            time.sleep(hold_time)
        else:
            queue.put(REGISTER_FAILED)
        
        # 完成后清理
        manager.cleanup_current_process()
        queue.put(PROCESS_COMPLETED)
        
    except Exception as e:
        queue.put(f"ERROR: {str(e)}")

def register_and_terminate_process(db_path, config_key, config_value, queue):
    """注册配置后立即终止进程"""
    try:
        manager = RuntimeWebhookConfigManager.get_singleton_instance(db_path=db_path)
        success = manager.register_config(config_key, config_value)
        if success:
            queue.put(REGISTER_SUCCESS)
            # 不清理，直接退出，模拟进程崩溃
    except Exception as e:
        queue.put(f"ERROR: {str(e)}")

def register_with_timeout_process(db_path, config_key, config_value, queue, sleep_time):
    """注册配置后睡眠超过心跳超时时间"""
    try:
        manager = RuntimeWebhookConfigManager.get_singleton_instance(db_path=db_path)
        success = manager.register_config(config_key, config_value)
        if success:
            queue.put(REGISTER_SUCCESS)
            # 睡眠超过心跳超时时间
            time.sleep(sleep_time)
            queue.put(PROCESS_COMPLETED)
    except Exception as e:
        queue.put(f"ERROR: {str(e)}")

# ====================== 单进程测试 ======================

def test_basic_register_unregister(config_manager):
    """测试基本的注册和注销功能"""
    # 注册配置项
    assert config_manager.register_config("test_key", "test_value"), "应该成功注册配置"
    
    # 检查是否已注册
    active_configs = config_manager.get_active_configs()
    assert "test_key" in active_configs, "配置项应该在活动配置中"
    assert "test_value" in active_configs["test_key"], "配置值应该在活动配置中"
    
    # 注销配置项
    assert config_manager.unregister_config("test_key", "test_value"), "应该成功注销配置"
    
    # 检查是否已注销
    active_configs = config_manager.get_active_configs()
    assert "test_key" not in active_configs or "test_value" not in active_configs.get("test_key", {}), "配置项应该已被注销"

def test_register_same_key_different_value(config_manager):
    """测试注册相同键不同值"""
    # 注册第一个配置
    assert config_manager.register_config("same_key", "value1"), "应该成功注册第一个配置"
    
    # 注册第二个配置（相同键不同值） 同一个进程 一个键只能有一个值
    assert not config_manager.register_config("same_key", "value2"), "应该成功注册第二个配置（相同键不同值）"
    
    # 检查两个配置是否都已注册
    active_configs = config_manager.get_active_configs()
    assert "same_key" in active_configs, "配置键应该在活动配置中"
    assert "value1" in active_configs["same_key"], "第一个配置值应该在活动配置中"
    assert "value2" not in active_configs["same_key"], "第二个配置值应该在活动配置中"
    
    # 清理
    config_manager.unregister_config("same_key", "value1")
    config_manager.unregister_config("same_key", "value2")

def test_register_same_key_same_value_fails(config_manager):
    """测试注册相同键相同值（应失败）"""
    # 注册第一个配置
    assert config_manager.register_config("dup_key", "dup_value"), "应该成功注册第一个配置"
    
    # 尝试注册相同的配置（应失败）
    assert not config_manager.register_config("dup_key", "dup_value"), "注册相同键值对应该失败"
    
    # 清理
    config_manager.unregister_config("dup_key", "dup_value")

def test_check_config_registrable(config_manager):
    """测试检查配置可用性功能"""
    # 初始状态下配置应该可用
    assert config_manager.check_config_registrable("check_key", "check_value"), "初始状态下配置应该可用"
    
    # 注册配置后应该不可用
    assert config_manager.register_config("check_key", "check_value"), "应该成功注册配置"
    assert not config_manager.check_config_registrable("check_key", "check_value"), "注册后配置应该不可用"
    
    # 注销后应该再次可用
    assert config_manager.unregister_config("check_key", "check_value"), "应该成功注销配置"
    assert config_manager.check_config_registrable("check_key", "check_value"), "注销后配置应该再次可用"

def test_get_active_configs_and_values(config_manager):
    """测试获取活动配置功能"""
    # 注册多个配置
    config_manager.register_config("key1", "value1")
    config_manager.register_config("key1", "value2")
    config_manager.register_config("key2", "value1")
    
    # 测试获取所有活动配置
    active_configs = config_manager.get_active_configs()
    assert "key1" in active_configs and "key2" in active_configs, "应该包含所有注册的键"
    assert "value1" in active_configs["key1"] and "value2" not in active_configs["key1"], "应该包含key1的所有值"
    assert "value1" in active_configs["key2"], "应该包含key2的值"
    
    # 测试获取特定键的活动值
    key1_values = config_manager.get_valid_values_for_key("key1")
    assert "value1" in key1_values and "value2" not in key1_values, "应该包含key1的所有活动值"
    
    # 清理
    config_manager.cleanup_current_process()

# ====================== 多进程测试 ======================

def test_multiprocess_same_key_different_value(temp_db_path):
    """测试多进程注册相同键不同值（应成功）"""
    # 创建队列用于进程间通信
    queue = multiprocessing.Queue()
    
    # 启动两个进程，注册相同键不同值
    p1 = multiprocessing.Process(
        target=register_config_process,
        args=(temp_db_path, "multi_key", "value1", queue, 4)
    )
    p2 = multiprocessing.Process(
        target=register_config_process,
        args=(temp_db_path, "multi_key", "value2", queue, 4)
    )
    
    p1.start()
    # time.sleep(0.5)
    p2.start()
    time.sleep(3)
    # 验证配置已注册
    manager = RuntimeWebhookConfigManager.get_singleton_instance(db_path=temp_db_path)
    active_configs = manager.get_active_configs()
    assert "multi_key" in active_configs, "配置键应该在活动配置中"
    assert "value1" in active_configs["multi_key"], "value1应该在活动配置中"
    assert "value2" in active_configs["multi_key"], "value2应该在活动配置中"
    # 收集结果
    results = []
    for _ in range(2):  # 每个进程会产生两个消息
        try:
            results.append(queue.get(timeout=5))
        except Exception: # noqa
            break
    
    # 验证结果
    assert results.count(REGISTER_SUCCESS) == 2, "两个进程都应该成功注册"
    
    # 清理
    p1.join()
    p2.join()
    

    
    # 清理
    manager.cleanup_current_process()

def test_multiprocess_same_key_same_value(temp_db_path):
    """测试多进程注册相同键相同值（后注册者应失败）"""
    # 创建队列用于进程间通信
    queue = multiprocessing.Queue()
    
    # 启动两个进程，注册相同键相同值
    p1 = multiprocessing.Process(
        target=register_config_process,
        args=(temp_db_path, "same_key_value", "same_value", queue, 2)
    )
    p2 = multiprocessing.Process(
        target=register_config_process,
        args=(temp_db_path, "same_key_value", "same_value", queue, 2)
    )
    
    p1.start()
    time.sleep(0.5)  # 确保第一个进程先注册
    p2.start()
    
    # 收集结果
    results = []
    for _ in range(4):  # 每个进程会产生两个消息
        try:
            results.append(queue.get(timeout=5))
        except Exception: # noqa
            break
    
    # 验证结果
    assert results.count(REGISTER_SUCCESS) == 1, "只有一个进程应该成功注册"
    assert results.count(REGISTER_FAILED) == 1, "一个进程应该注册失败"
    
    # 清理
    p1.join()
    p2.join()
    
    # 清理数据库
    manager = RuntimeWebhookConfigManager.get_singleton_instance(db_path=temp_db_path)
    manager.cleanup_current_process()

def test_process_termination_releases_config(temp_db_path):
    """测试进程终止后配置项自动释放"""
    # 创建队列用于进程间通信
    queue = multiprocessing.Queue()
    
    # 启动进程注册配置后终止
    p = multiprocessing.Process(
        target=register_and_terminate_process,
        args=(temp_db_path, "term_key", "term_value", queue)
    )
    p.start()
    
    # 等待进程注册配置
    result = queue.get(timeout=5)
    assert result == REGISTER_SUCCESS, "进程应该成功注册配置"
    
    # 终止进程
    p.terminate()
    p.join(timeout=2)
    
    # 等待一段时间确保系统有机会清理资源
    time.sleep(2)
    
    # 创建新的管理器实例
    manager = RuntimeWebhookConfigManager.get_singleton_instance(db_path=temp_db_path)
    
    # 清理非活动记录
    def _cleanup(cur):
        manager._cleanup_inactive_records(cur)
    
    manager._exec_retry(_cleanup)
    
    # 检查配置是否已释放
    assert manager.check_config_registrable("term_key", "term_value"), "进程终止后配置应该被释放"
    
    # 尝试注册相同配置
    assert manager.register_config("term_key", "term_value"), "应该能够注册之前被终止进程持有的配置"
    
    # 清理
    manager.cleanup_current_process()

def test_heartbeat_timeout_releases_config(temp_db_path):
    """测试心跳超时后配置项自动释放"""
    # 创建队列用于进程间通信
    queue = multiprocessing.Queue()
    
    # 创建管理器实例并修改心跳超时时间为较短的值（便于测试）
    manager = RuntimeWebhookConfigManager.get_singleton_instance(db_path=temp_db_path)
    original_timeout = manager.HEARTBEAT_TIMEOUT
    manager.HEARTBEAT_TIMEOUT = 3  # 设置为3秒
    
    # 启动进程注册配置后睡眠超过心跳超时时间
    p = multiprocessing.Process(
        target=register_with_timeout_process,
        args=(temp_db_path, "timeout_key", "timeout_value", queue, 5)  # 睡眠5秒
    )
    p.start()
    
    # 等待进程注册配置
    result = queue.get(timeout=5)
    assert result == REGISTER_SUCCESS, "进程应该成功注册配置"
    
    # 等待超过心跳超时时间
    time.sleep(6)
    
    # 清理非活动记录
    def _cleanup(cur):
        manager._cleanup_inactive_records(cur)
    
    manager._exec_retry(_cleanup)
    
    # 检查配置是否已释放
    assert manager.check_config_registrable("timeout_key", "timeout_value"), "心跳超时后配置应该被释放"
    
    # 尝试注册相同配置
    assert manager.register_config("timeout_key", "timeout_value"), "应该能够注册之前因心跳超时而释放的配置"
    
    # 恢复原始超时时间
    manager.HEARTBEAT_TIMEOUT = original_timeout
    
    # 清理
    p.join()
    manager.cleanup_current_process()

def test_process_lifecycle_config_reuse(temp_db_path):
    """测试进程生命周期结束后新进程使用相同配置"""
    # 创建队列用于进程间通信
    queue = multiprocessing.Queue()
    
    # 第一个进程注册配置
    p1 = multiprocessing.Process(
        target=register_config_process,
        args=(temp_db_path, "lifecycle_key", "lifecycle_value", queue, 2)
    )
    p1.start()
    
    # 等待第一个进程注册配置
    result1 = queue.get(timeout=5)
    assert result1 == REGISTER_SUCCESS, "第一个进程应该成功注册配置"
    
    # 等待第一个进程完成
    result2 = queue.get(timeout=5)
    assert result2 == PROCESS_COMPLETED, "第一个进程应该正常完成"
    
    # 第二个进程尝试注册相同配置
    p2 = multiprocessing.Process(
        target=register_config_process,
        args=(temp_db_path, "lifecycle_key", "lifecycle_value", queue, 2)
    )
    p2.start()
    
    # 等待第二个进程注册配置
    result3 = queue.get(timeout=5)
    assert result3 == REGISTER_SUCCESS, "第二个进程应该成功注册配置（第一个进程已完成）"
    
    # 清理
    p1.join()
    p2.join()
    
    # 清理数据库
    manager = RuntimeWebhookConfigManager.get_singleton_instance(db_path=temp_db_path)
    manager.cleanup_current_process()

if __name__ == "__main__":
    pytest.main(["-xvs", __file__])
