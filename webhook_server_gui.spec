# -*- mode: python ; coding: utf-8 -*-
"""
PyInstaller spec file for Webhook Server GUI Application
Supports Windows, Linux, and macOS bundling
"""

import os
import sys
from pathlib import Path

sys_platform = sys.platform
spec_path = Path(sys.argv[0]).resolve()
project_root = Path(spec_path).parent.absolute()
src_path = project_root / "src"
# 添加整个src目录到Python路径
sys.path.insert(0, str(src_path))
# 确定主入口文件
main_script = str(src_path / "webhook_server" / "config_selection_gui.py")
# 英文: 多类别设备数据接收存储服务端
software_name='NexusRecv'
# 图标配置
win_icon=str(project_root / "resources" / "x.ico")
mac_icon=str(project_root / "resources" / "x.icns")
linux_icon=str(project_root / "resources" / "x.png")

# 数据文件配置 - 22.png,x.ico 打开之后显示的图标
datas = [(str(project_root / "resources" / "22.png"), "resources"),
          (win_icon, "resources"),
          (mac_icon, "resources"),
          (linux_icon, "resources")]

if sys_platform.startswith('linux'):
    desktop_source = project_root / "resources" / f"{software_name}.desktop"
    if desktop_source.exists():
        datas.append((str(desktop_source), '.'))
    else:
        # Create desktop file dynamically if not found
        desktop_file_content = f"""
[Desktop Entry]
Type=Application
Name={software_name}
Comment=Webhook Server GUI
Exec=./{software_name}
Icon=resources/x.png  # 修正为相对路径
Terminal=false
Categories=Utility;
"""
        # 将内容添加到datas中，它将在构建时被创建并放置在dist目录的根下
        datas.append(
            (f"{software_name}.desktop", desktop_file_content.encode('utf-8'), 'DATA')
        )


block_cipher = None



# 隐藏导入模块,'jaraco.path', 'jaraco.context'
hiddenimports = [
      'requests','future','aiohttp','aiohttp.web',
      'logging.handlers','multiprocessing','multiprocessing.pool'
]

# 排除的模块
excludes = [
    'unittest','test','tests','pytest','numpy','matplotlib',
    'scipy','pandas','jupyter','IPython','setuptools._vendor.packaging.licenses',
]

# 分析配置
a = Analysis(
    [main_script],
    pathex=[str(src_path)],
    binaries=[],
    datas=datas,
    hiddenimports=hiddenimports,
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=excludes,
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

# 过滤不需要的文件
def filter_binaries(binaries):
    """过滤不需要的二进制文件:仅限Windows"""
    filtered = []
    exclude_patterns = ['api-ms-win-','ucrtbase','_testcapi','_testimportmultiple','_testmultiphase']
    
    for name, path, typecode in binaries:
        should_exclude = any(pattern in name.lower() for pattern in exclude_patterns)
        if not should_exclude:
            filtered.append((name, path, typecode))
    
    return filtered

if sys_platform == 'win32':
    a.binaries = filter_binaries(a.binaries)

# 创建PYZ归档
pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)
# 平台特定的图标配置
exe_icon = None
if sys.platform == 'win32':
    exe_icon = win_icon

exe = EXE(
    pyz,
    a.scripts,
    [],
    exclude_binaries=True,
    name=software_name,
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,  # GUI应用不显示控制台
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=exe_icon, # 仅Windows设置
)


coll = COLLECT(
        exe,
        a.binaries,
        a.zipfiles,
        a.datas,
        strip=False,
        upx=True,
        upx_exclude=[],
        name=software_name
    )

if sys_platform == 'darwin':  # macOS
    mac_app_name = f'{software_name}.app'
    app_bundle = BUNDLE(
        coll,
        name=mac_app_name,
        icon=mac_icon, # .icns格式图标
        bundle_identifier='com.nexusrecv.gui',
        info_plist={
            'CFBundleName': software_name,
            'CFBundleDisplayName': 'NexusRecv WebHook Server',
            'CFBundleVersion': '1.0.0',
            'CFBundleShortVersionString': '1.0.0',
            'NSHighResolutionCapable': 'True',
            'LSMinimumSystemVersion': '10.13.0',
            'CFBundleDevelopmentRegion': 'en',
            'CFBundleExecutable': software_name,
        },
        codesign_identity='-',
        entitlements_file=None
    )

