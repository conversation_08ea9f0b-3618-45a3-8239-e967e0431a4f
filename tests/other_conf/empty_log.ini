[log]
level=
console=false
#日志是否输出到文件中
; file=false
#日志文件存放目录
dir=../logs
#日志文件名:[前缀-log级别-日期.log]
filename_prefix=webhook-1
filename_date_fmt=%Y-%m-%d
filename=%(filename_prefix)s-%(levelname)s-%(filename_date_fmt)s.log
max_size=5MB
backup_count=3
format=%(asctime)s [%(levelname)s] %(message)s
date_fmt=%Y-%m-%d %H:%M:%S
#日志保留天数[其删除也只会删除指定filename_prefix前缀的日志文件]
expire_logs_days=5
#各个级别在控制台输出的颜色
[colors]
DEBUG=LIGHTBLACK_EX
INFO=LIGHTWHITE_EX
WARNING=YELLOW
ERROR=RED
CRITICAL=BRIGHT+WHITE
