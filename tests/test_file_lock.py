import os
import subprocess
import sys
import tempfile
import threading
import time
from shlex import quote
from typing import Tuple

import pytest

# 确保能够导入 src 目录下的模块
src_path = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', 'src'))
if src_path not in sys.path:
    sys.path.append(src_path)

from common.utils.file_lock import FileLock, exclusive_file_access, is_file_locked
from config import gui_constants

# ====================== 辅助函数 ======================

@pytest.fixture
def temp_file():
    """创建临时文件用于测试"""
    fd, path = tempfile.mkstemp()
    os.close(fd)
    yield path
    # 清理
    try:
        os.remove(path)
    except (FileNotFoundError, PermissionError):
        pass
    # try:
    #     os.remove(path + '.lock')
    # except (FileNotFoundError, PermissionError):
    #     pass

@pytest.fixture
def temp_dir():
    """创建临时目录用于测试"""
    path = tempfile.mkdtemp()
    yield path
    # 清理
    try:
        os.rmdir(path)
    except (FileNotFoundError, PermissionError):
        pass

def run_in_subprocess(file_path, blocking=False, timeout=1, wait_time=0)-> Tuple[str, int]:# noqa
    """在子进程中运行指定的函数"""
    script = f"""
import sys
import time
import os

sys.path.append({quote(src_path)})
from webhook_server.utils.file_lock import FileLock

def run_test():
    try:
        with FileLock(r{quote(file_path)}, blocking={blocking}, timeout={timeout}) as lock:
            print('LOCK_ACQUIRED')
            time.sleep({wait_time})
            print('RELEASING_LOCK')
        print('LOCK_RELEASED')
    except Exception as e:
        print(f'ERROR: {{str(e)}}')
        return 1
    return 0

sys.exit(run_test())
"""

    with tempfile.NamedTemporaryFile('w', suffix='.py', delete=False) as f:
        f.write(script)
        temp_script = f.name

    try:
        result= subprocess.run([sys.executable, temp_script],
                              capture_output=True, text=True, timeout=timeout+2)
        return result.stdout, result.returncode
    finally:
        try:
            os.unlink(temp_script)
        except:# noqa
            pass

# ====================== 基本功能测试 ======================

def test_basic_lock_and_release(temp_file):
    """测试基本的锁获取和释放功能"""
    lock = FileLock(temp_file)
    assert lock.lock_acquired, "锁应该被成功获取"
    assert is_file_locked(temp_file), "文件应该被标记为已锁定"
    
    lock.release()
    assert not lock.lock_acquired, "锁应该被成功释放"
    assert not is_file_locked(temp_file), "文件应该被标记为未锁定"

def test_context_manager(temp_file):
    """测试上下文管理器功能"""
    with FileLock(temp_file) as lock:
        assert lock.lock_acquired, "在上下文中锁应该被获取"
        assert is_file_locked(temp_file), "文件应该被标记为已锁定"
    
    # 上下文退出后锁应该被释放
    assert not is_file_locked(temp_file), "上下文退出后文件应该被标记为未锁定"

def test_exclusive_file_access(temp_file):
    """测试exclusive_file_access上下文管理器"""
    with exclusive_file_access(temp_file) as lock:
        assert lock.lock_acquired, "在上下文中锁应该被获取"
        assert is_file_locked(temp_file), "文件应该被标记为已锁定"
        
        # 测试文件访问
        with open(temp_file, 'w') as f:
            f.write("测试内容")
    
    # 上下文退出后锁应该被释放
    assert not is_file_locked(temp_file), "上下文退出后文件应该被标记为未锁定"

def test_is_file_locked_function(temp_file):
    """测试is_file_locked函数在不同状态下的行为"""
    # 初始状态：未锁定
    assert not is_file_locked(temp_file), "新创建的文件不应该被锁定"
    
    # 获取锁后：已锁定
    lock = FileLock(temp_file)
    assert is_file_locked(temp_file), "加锁后文件应该被标记为已锁定"
    
    # 释放锁后：未锁定
    lock.release()
    assert not is_file_locked(temp_file), "释放锁后文件应该被标记为未锁定"

# ====================== 多进程竞争测试 ======================

def test_multiprocess_lock_competition(temp_file):
    """测试两个进程竞争同一个文件锁"""
    print(f"src:{src_path}")
    # 第一个进程获取锁
    lock = FileLock(temp_file)
    assert lock.lock_acquired, "第一个进程应该成功获取锁"
    
    # 第二个进程尝试获取锁（非阻塞模式）
    output, return_code = run_in_subprocess(file_path=temp_file, blocking=False)
    assert "ERROR" in output, "第二个进程应该无法获取锁并报错"
    assert return_code != 0, "第二个进程应该以非零状态退出"

    # 释放第一个进程的锁
    lock.release()

    # 再次尝试第二个进程
    output, return_code = run_in_subprocess(file_path=temp_file, blocking=False)
    assert "LOCK_ACQUIRED" in output, "释放锁后第二个进程应该能获取锁"
    assert "LOCK_RELEASED" in output, "第二个进程应该能释放锁"
    assert return_code == 0, "第二个进程应该正常退出"

def test_blocking_mode_with_timeout(temp_file):
    """测试阻塞模式下的超时行为"""
    # 第一个进程获取锁并持有一段时间
    lock = FileLock(temp_file)

    # 第二个进程尝试获取锁（阻塞模式，较短超时）
    output, return_code = run_in_subprocess( temp_file, blocking=True, timeout=1)
    assert "ERROR" in output, "第二个进程应该因超时而失败"
    assert "timeout waiting for lock" in output, "应该显示超时错误消息"

    # 释放锁
    lock.release()

    # 第三个进程尝试获取锁（阻塞模式，足够的超时时间）
    output, return_code = run_in_subprocess(temp_file, blocking=True, timeout=5)
    assert "LOCK_ACQUIRED" in output, "释放锁后第三个进程应该能获取锁"
    assert return_code == 0, "第三个进程应该正常退出"

def test_lock_release_after_process_exit(temp_file):
    """测试进程退出后锁是否正确释放"""
    # 启动子进程获取锁并持有一段时间
    output, return_code = run_in_subprocess(temp_file, wait_time=1)
    assert "LOCK_ACQUIRED" in output, "子进程应该能获取锁"
    
    # 等待子进程完成
    time.sleep(2)
    
    # 验证锁是否被释放
    assert not is_file_locked(temp_file), "子进程退出后锁应该被释放"
    
    # 当前进程应该能够获取锁
    lock = FileLock(temp_file)
    assert lock.lock_acquired, "子进程退出后当前进程应该能获取锁"
    lock.release()

# ====================== 异常处理测试 ======================

def test_invalid_file_path():
    """测试无效文件路径的处理"""
    with pytest.raises(ValueError):
        FileLock("")  # 空路径


def test_directory_as_lock_file(temp_dir):
    """测试目录作为锁文件的处理"""
    with pytest.raises(ValueError):
        FileLock(temp_dir)

def test_symlink_as_lock_file(temp_file):
    """测试符号链接作为锁文件的处理"""
    symlink_path = temp_file + ".symlink"
    try:
        os.symlink(temp_file, symlink_path)
        
        # 锁定原始文件
        lock = FileLock(temp_file)
        assert lock.lock_acquired, "应该能锁定原始文件"
        
        # 尝试通过符号链接锁定
        with pytest.raises(Exception):
            FileLock(symlink_path)
        
        lock.release()
    except OSError:
        pytest.skip("创建符号链接失败，可能需要管理员权限")
    finally:
        try:
            if os.path.exists(symlink_path):
                os.unlink(symlink_path)
        except:# noqa
            pass

def test_same_process_double_lock(temp_file):
    """测试同一进程对同一文件多次加锁"""
    lock1 = FileLock(temp_file)
    assert lock1.lock_acquired, "第一次锁定应该成功"
    
    # 同一进程再次尝试锁定同一文件应该失败
    with pytest.raises(RuntimeError):
         FileLock(temp_file)
    
    lock1.release()
    assert not lock1.lock_acquired, "锁应该被释放"
    
    # 释放后应该可以再次锁定
    lock3 = FileLock(temp_file)
    assert lock3.lock_acquired, "释放后再次锁定应该成功"
    lock3.release()

# ====================== 边缘情况测试 ======================

def test_rapid_lock_unlock(temp_file):
    """测试快速连续加锁解锁"""
    for _ in range(50):  # 连续进行50次加锁解锁操作
        lock = FileLock(temp_file)
        assert lock.lock_acquired, "应该能成功获取锁"
        lock.release()
        assert not lock.lock_acquired, "应该能成功释放锁"

def test_large_file_lock(temp_file):
    """测试大文件锁定性能"""
    # 创建一个10MB的文件
    with open(temp_file, 'wb') as f:
        f.write(b'0' * (10 * 1024 * 1024))
    
    start_time = time.time()
    lock = FileLock(temp_file)
    lock_time = time.time() - start_time
    
    assert lock.lock_acquired, "应该能锁定大文件"
    assert lock_time < 1.0, f"锁定大文件不应该花费太多时间 (花费了 {lock_time:.2f}秒)"
    
    lock.release()

def test_concurrent_threads_same_process(temp_file):
    """测试同一进程中多线程并发访问"""
    lock = FileLock(temp_file)
    assert lock.lock_acquired, "主线程应该能获取锁"
    
    def thread_func():
        # 同一进程的其他线程尝试获取锁应该失败
        with pytest.raises(RuntimeError):
            FileLock(temp_file)
    
    thread = threading.Thread(target=thread_func)
    thread.start()
    thread.join()
    
    lock.release()

def demo_file_lock(file_path):
    try:
        # 使用上下文管理器确保锁的释放
        with FileLock(file_path, blocking=True) as lock:
            print(f"Working with file: {file_path}")
            print(f"Lock status: {lock}")

            # 模拟文件操作
            with open(file_path, 'a') as f:
                f.write(f"Locked by process at {time.ctime()}\n")

            # 检查锁状态
            if is_file_locked(file_path):
                print("File is locked (as expected)")
            else:
                print("File lock check failed!")

            # 模拟长时间操作
            input("Press Enter to release lock...")

            print("Operation completed")

    except RuntimeError as e:
        print(f"Lock error: {str(e)}")
    except Exception as e:
        print(f"Unexpected error: {str(e)}")
    finally:
        # 最终检查锁状态
        if is_file_locked(file_path):
            print("File is still locked after operation!")
        else:
            print("File lock released successfully")

def test_demo_file_lock(temp_file):
    """实际对一个文件进行加锁和解锁的例子"""
    demo_file_lock("D:\Git\python-samples-hub\README.md")
# ====================== 跨平台兼容性测试 ======================

def test_platform_specific_behavior(temp_file):
    """测试在不同平台上的行为"""
    # 这个测试会在所有平台上运行，但会根据平台执行不同的断言
    lock = FileLock(temp_file)
    
    if gui_constants.IS_WINDOWS:
        # Windows特定断言
        assert hasattr(lock, '_acquire_lock_win32'), "Windows平台应该有_acquire_lock_win32方法"
        # 可以添加更多Windows特定的测试
    else:
        # Unix/Linux特定断言
        assert hasattr(lock, '_acquire_lock_unix'), "Unix平台应该有_acquire_lock_unix方法"
        # 可以添加更多Unix特定的测试
    
    lock.release()

# 如果需要，可以添加更多测试...
