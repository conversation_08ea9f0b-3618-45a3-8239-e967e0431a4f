# cpu利用率测试

import os
import time

import psutil


def monitor_process():
    # 获取当前进程PID
    pid = os.getpid()
    process = psutil.Process(pid)

    print(f"监控进程 PID: {pid} (按Ctrl+C停止)")
    print("{:<8} {:<15} {:<10} {:<15}".format("时间", "内存(MB)", "CPU(%)", "状态"))

    try:
        # 初始化CPU使用率计算
        process.cpu_percent(interval=5.0)

        while True:
            # 获取内存信息（工作集内存）
            mem_info = process.memory_info()
            working_set_mb = mem_info.rss / (1024 * 1024)  # RSS = 工作集内存

            # 获取CPU使用率（阻塞1秒获取真实值）
            cpu_percent = process.cpu_percent(interval=5.0)

            # 获取进程状态
            status = "运行中" if process.status() == psutil.STATUS_RUNNING else "其他"

            # 打印实时信息
            timestamp = time.strftime("%H:%M:%S")
            print("{:<8} {:<15.2f} {:<10.3f} {:<15}".format(
                timestamp, working_set_mb, cpu_percent, status))

    except KeyboardInterrupt:
        print("\n监控已停止")
    except Exception as e:
        print(f"监控出错: {str(e)}")

if __name__ == "__main__":
    monitor_process()
