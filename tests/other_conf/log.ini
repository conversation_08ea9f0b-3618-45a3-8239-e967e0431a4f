[log]
level=INFO
console=true
#日志是否输出到文件中
file=true
#日志文件存放目录
dir=D:\Git\python-samples-hub\logs
#日志文件名:[前缀-log级别-日期.log]
filename_prefix=webhook
filename_date_fmt=%Y-%m-%d
filename=%(filename_prefix)s-%(levelname)s-%(filename_date_fmt)s.log
max_size=5MB
backup_count=3
format=%(asctime)s %(levelname)8s %(message)s
date_fmt=%Y-%m-%d %H:%M:%S
zone=Asia/Shanghai
#日志保留天数[其删除也只会删除指定filename_prefix前缀的日志文件]
expire_logs_days=5
#各个级别在控制台输出的颜色
[colors]
DEBUG=LIGHTBLACK_EX
INFO=LIGHTWHITE_EX
WARNING=YELLOW
ERROR=RED
CRITICAL=BRIGHT+WHITE
