cd # PyInstaller打包所需的依赖
# 在打包前请安装这些依赖：pip install -r scripts/build/requirements_build.txt

# 核心打包工具
pyinstaller>=6.0.0

# 如果需要UPX压缩（可选）
# upx-ucl  # 需要单独安装UPX工具

# Linux deb包创建工具（Linux平台）
# 在Ubuntu/Debian上安装：sudo apt-get install dpkg-dev

# Windows特定工具（可选）
# pywin32>=306  # Windows平台增强功能

# 项目依赖（确保与requirements.txt一致）
APScheduler>=3.11.0
colorama>=0.4.6
fastapi>=0.115.12
psutil>=5.9.7
pydantic>=2.11.4
Requests>=2.32.3
typing_extensions>=4.13.2
ulid_py>=1.1.0
uvicorn>=0.34.2
future>=0.18.3
pillow>=10.2.0
aiohttp>=3.9.1
asyncio~=3.4.3
chardet~=5.2.0
ttkbootstrap~=1.13.12
pystray>=0.19.0
