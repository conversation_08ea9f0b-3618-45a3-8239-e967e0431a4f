# 腾讯滑块验证码识别器

基于 Playwright 和 ddddocr API 实现的滑块拼图验证码自动识别和操作工具。

## 功能特性

- 🎯 **滑块验证码识别**: 支持多种滑块拼图验证码的自动识别
- 🤖 **浏览器自动化**: 使用 Playwright 控制浏览器进行自动操作
- 🔍 **多种识别模式**: 支持 slideComparison、capcode 等多种 API 接口
- 📝 **文字识别**: 支持图片中文字内容的识别
- 🎨 **目标检测**: 检测图片中文字和图标的坐标位置
- 🧮 **数学计算**: 识别并计算图片中的算术表达式
- 📍 **点击位置**: 分析图片并返回最佳点击位置

## 依赖服务

### ddddocr API 服务
- **服务地址**: http://************:7777
- **API 接口**:
  - `/slideComparison`: 传入缺失滑块图和完整背景图，返回滑块目标位置
  - `/capcode`: 传入滑块图和缺失滑块的背景图，返回滑块目标位置
  - `/classification`: 识别图片结果
  - `/detection`: 获取背景图中所有文字和图标的坐标位置
  - `/calculate`: 计算图片中的算数结果
  - `/select`: 返回图片点击结果

## 安装依赖

```bash
# 安装 Python 依赖
pip install playwright pillow requests

# 安装 Playwright 浏览器
playwright install chromium
```

## 快速开始

### 1. 腾讯滑块验证码（推荐）

```python
import asyncio
from src.captcha.recognizer.t_sec import TencentSliderSolver

async def main():
    # 创建腾讯滑块解决器
    solver = TencentSliderSolver(
        ddddocr_base_url="http://************:7777",
        headless=False  # 显示浏览器
    )

    async with solver:
        # 导航到包含验证码的页面
        await solver.navigate_to_page("https://example.com/login")

        # 解决滑块验证码
        success = await solver.solve_captcha(max_retries=3)

        print(f"验证码解决结果: {'成功' if success else '失败'}")

asyncio.run(main())
```

### 2. 通用滑块验证码

```python
import asyncio
from src.captcha.recognizer.t_sec import solve_slider_captcha

async def main():
    # 解决滑块验证码
    success = await solve_slider_captcha(
        url="https://example.com/captcha",
        background_selector=".captcha-background img",
        slider_button_selector=".slider-button",
        slider_selector=".slider-piece img",
        success_selector=".captcha-success",
        failure_selector=".captcha-failure"
    )

    print(f"验证码解决结果: {'成功' if success else '失败'}")

asyncio.run(main())
```

### 3. 高级使用

```python
import asyncio
from src.captcha.recognizer.t_sec import CaptchaSolver

async def advanced_usage():
    solver = CaptchaSolver(
        ddddocr_base_url="http://************:7777",
        headless=False,  # 显示浏览器窗口
        browser_type="chromium"
    )

    try:
        # 解决滑块验证码
        success = await solver.solve_slider_captcha_on_page(
            url="https://example.com/login",
            background_selector=".captcha-bg",
            slider_button_selector=".slider-btn",
            max_retries=5
        )

        if success:
            print("滑块验证码解决成功！")
        else:
            print("滑块验证码解决失败")

    finally:
        solver.close()

asyncio.run(advanced_usage())
```

### 4. 文字识别

```python
from src.captcha.recognizer.t_sec import recognize_text

# 识别图片中的文字
text = recognize_text("captcha_image.png")
print(f"识别结果: {text}")
```

### 5. 直接使用 API 客户端

```python
from src.captcha.recognizer.t_sec import DDDDOCRClient

client = DDDDOCRClient("http://************:7777")

try:
    # 滑块位置识别
    x = client.slide_comparison("slider.png", "background.png")
    print(f"滑块目标位置: x={x}")

    # 验证码识别（另一种方式）
    x = client.capcode("slider.png", "background.png")
    print(f"验证码识别结果: x={x}")

    # 文字识别
    text = client.classification("text_image.png")
    print(f"识别文字: {text}")

    # 目标检测
    objects = client.detection("image.png")
    print(f"检测到 {len(objects)} 个对象")
    for bbox in objects:
        print(f"  坐标: {bbox}")  # [x1, y1, x2, y2]

    # 选择识别
    selections = client.select("image.png")
    print(f"识别到 {len(selections)} 个选择项")
    for item in selections:
        print(f"  项目: {item}")  # {文字: [x1, y1, x2, y2]}

finally:
    client.close()
```

## 测试

### 测试 ddddocr 服务连接

```bash
cd src/captcha/recognizer/t_sec
python test_ddddocr_connection.py
```

### 测试腾讯滑块验证码

```bash
cd src/captcha/recognizer/t_sec
python test_tencent_captcha.py
```

### 运行腾讯滑块验证码示例

```bash
cd src/captcha/recognizer/t_sec
python tencent_example.py
```

### 通用滑块验证码测试

```bash
cd src/captcha/recognizer/t_sec
python test_slider_captcha.py
```

## 注意事项

⚠️ **重要提醒**:
- 由于风控的存在，大多数验证码识别都有失败率
- 大公司和强验证码场景下容易失败
- 当前项目主要适用于小网站的验证码识别
- 请遵守相关网站的使用条款和法律法规

## 参考项目

- 腾讯官方验证码平台: https://cloud.tencent.com/product/captcha
- 滑块验证码项目: https://github.com/chenwei-zhao/captcha-recognizer
- 谷歌验证码项目: https://github.com/Vinyzu/recognizer

## 技术栈

- **Playwright**: 浏览器自动化
- **ddddocr**: 验证码识别服务
- **PIL/Pillow**: 图像处理
- **requests**: HTTP 客户端
