"""软件设置对话框组件。

负责显示和管理软件的设置选项。
"""
import configparser
import os
from tkinter import messagebox
from typing import Optional

import ttkbootstrap as ttkb
from ttkbootstrap.constants import *

from common.utils import ttkb_gui_utils, logging_utils, config_utils, gui_utils, common_utils
from webhook_server.config import gui_constants
from webhook_server.models import gui_properties


class SoftwareSettingsDialogComponent:
    """软件设置对话框组件类。"""

    def __init__(self, props: gui_properties.GUIProperties):
        """初始化软件设置对话框组件。

        Args:
            props: GUI属性管理器实例
        """
        self.props = props
        self.settings_dialog: Optional[ttkb.Toplevel] = None
        self.minimize_to_tray_var: Optional[ttkb.BooleanVar] = None

        # 软件配置
        self.software_config = {}
        self.original_config = {}
        # 加载软件配置
        self.load_software_config()

    def load_software_config(self):
        """加载软件配置文件"""
        try:
            if os.path.exists(gui_constants.SOFTWARE_CONFIG_PATH):
                try:
                    software_config_parser = configparser.ConfigParser(interpolation=None)
                    # key保持原本的大小写
                    software_config_parser.optionxform = str
                    software_config_parser.read(gui_constants.SOFTWARE_CONFIG_PATH, encoding="utf-8")
                    # 加载软件设置项
                    if software_config_parser.has_section(gui_constants.SOFTWARE_CONFIG_FILE_SECTION_NAME):
                        self.software_config = config_utils.section_to_dict(software_config_parser, gui_constants.SOFTWARE_CONFIG_FILE_SECTION_NAME, True)
                except Exception as server_config_ex:
                    self.props.show_operation_error(error=server_config_ex, error_msg="加载服务端配置失败",parent_gui=self.props.root,no_exit=False)

            # 补充默认值
            for required_key in gui_constants.REQUIRED_SOFTWARE_CONFIG_KEYS:
                if required_key not in self.software_config or self.software_config[required_key] is None:
                    self.software_config[required_key] = gui_constants.SOFTWARE_CONFIG_DEFAULT_VALUES[required_key]

            # software_config 转换成具体类型
            minimize_to_tray_value = self.software_config[gui_constants.MINIMIZE_TO_TRAY_KEY]
            if isinstance(minimize_to_tray_value, str):
                self.software_config[gui_constants.MINIMIZE_TO_TRAY_KEY] = common_utils.str_to_bool(minimize_to_tray_value)

            # 将可能缺失的默认值补充到 软件设置文件中
            self.save_software_config()
        except Exception as e:
            logging_utils.logger_print(
                msg=f"failed to load software config: {e}", 
                custom_logger=self.props.logger
            )

    def save_software_config(self):
        """保存软件配置到文件"""
        try:
            config_utils.update_config(
                gui_constants.SOFTWARE_CONFIG_PATH,
                gui_constants.SOFTWARE_CONFIG_FILE_SECTION_NAME,
                self.software_config
            )
            self.original_config = self.software_config.copy()
            logging_utils.logger_print(
                msg=f"software config saved: {self.software_config}", 
                custom_logger=self.props.logger
            )
        except Exception as e:
            logging_utils.logger_print(
                msg=f"failed to save software config: {e}", 
                custom_logger=self.props.logger
            )

    def get_minimize_to_tray_setting(self) -> bool:
        """获取最小化到托盘设置
        
        Returns:
            bool: True表示最小化到托盘，False表示直接关闭
        """
        return self.software_config.get(gui_constants.MINIMIZE_TO_TRAY_KEY, gui_constants.SOFTWARE_CONFIG_DEFAULT_VALUES[gui_constants.MINIMIZE_TO_TRAY_KEY])

    def open_software_settings_dialog(self):
        """打开软件设置对话框"""
        if getattr(self, "settings_dialog", None) and self.settings_dialog.winfo_exists():
            self.settings_dialog.lift()
            return

        # 保存打开对话框时的原始配置状态


        # 创建对话框窗口，使用与ConfigDialogComponent相同的大小设置方式
        self.settings_dialog = ttkb.Toplevel(
            master=self.props.root,
            title="软件设置",
            size=gui_constants.SOFTWARE_CONFIG_DIALOG_SIZE,
            takefocus=True,
            topmost=True,
            resizable=(False, False),
            transient=self.props.root
        )
        ttkb_gui_utils.comm_child_win_do(self.settings_dialog, self.props.root)

        # 主框架，使用与ConfigDialogComponent相同的布局
        main_frame = ttkb.Frame(self.settings_dialog)
        main_frame.pack(fill=BOTH, expand=True, padx=20, pady=10)

        # 使用 grid 布局统一控制整体布局，与ConfigDialogComponent保持一致
        settings_frame = ttkb.LabelFrame(main_frame, text="软件设置")
        settings_frame.grid(row=0, column=0, sticky=NSEW, padx=(0,10), pady=(0,10))

        # 最小化到托盘选项，使用grid布局
        self.minimize_to_tray_var = ttkb.BooleanVar(value=self.software_config[gui_constants.MINIMIZE_TO_TRAY_KEY])

        minimize_label = ttkb.Label(settings_frame, text="关闭主界面最小化到任务栏:")
        minimize_label.grid(row=0, column=0, sticky=E, padx=5, pady=5)

        minimize_checkbutton = ttkb.Checkbutton(settings_frame,variable=self.minimize_to_tray_var,bootstyle="success-round-toggle") # noqa
        minimize_checkbutton.grid(row=0, column=1, sticky=W, padx=(5,90), pady=5)

        # 保存按钮框架，与ConfigDialogComponent保持一致
        save_frame = ttkb.Frame(main_frame)
        save_frame.grid(row=1, column=0, sticky=E, pady=(10, 0))

        ttkb.Button(save_frame, text="保存软件设置", command=self.save_settings).pack(anchor=E, padx=10)

        # 关闭事件
        self.settings_dialog.protocol("WM_DELETE_WINDOW", self.close_dialog)

    def save_settings(self):
        """保存软件设置"""
        # 只有点击保存按钮才会保存配置到文件
        if self.minimize_to_tray_var:
            self.software_config[gui_constants.MINIMIZE_TO_TRAY_KEY] = self.minimize_to_tray_var.get()
            self.save_software_config()
            logging_utils.logger_print(
                msg=f"software settings saved: {self.software_config}",
                custom_logger=self.props.logger
            )
        if messagebox.askyesno("成功","软件设置保存成功,是否退出软件设置界面?",parent=self.settings_dialog):
            gui_utils.gui_close(self.settings_dialog)

    def close_dialog(self):
        """直接关闭软件配置界面，不保存更改，恢复原始配置"""
        # 恢复原始配置状态
        self.software_config = self.original_config.copy()
        logging_utils.logger_print(
            msg=f"software settings cancelled, restored to: {self.software_config}",
            custom_logger=self.props.logger
        )

        gui_utils.gui_close(self.settings_dialog)
