# ⚙️ NexusRecv 配置文件说明

[![Config Format](https://img.shields.io/badge/format-INI-blue.svg)](README.md)
[![Encoding](https://img.shields.io/badge/encoding-UTF--8-green.svg)](README.md)
[![Multi-Instance](https://img.shields.io/badge/multi--instance-supported-orange.svg)](README.md)

## 📋 概述

NexusRecv使用INI格式的配置文件进行系统配置，支持多配置文件管理和跨进程配置同步。配置文件采用UTF-8编码，提供灵活的配置管理机制。

### 🎯 配置特性
- **📄 INI格式**: 标准INI格式，易于编辑和维护
- **🔄 多实例支持**: 支持多个配置文件同时运行
- **🔒 配置锁定**: 运行时配置锁定，防止冲突
- **✅ 自动验证**: 启动时自动验证配置项
- **🌍 国际化**: 支持中文配置和注释

## 📝 配置文件结构

### 🏗️ 基本结构

```ini
# NexusRecv 配置文件
# 编码: UTF-8
# 版本: v1.0.2

[server]
# 服务器基础配置
api_key = your_api_key_here
whitelist = ***********/24,127.0.0.1,*
host = 0.0.0.0
port = 8000

# 数据库配置
message_data_table_name = message_data_1
expire_data_days = 7
data_limit_num = 100000

# 系统配置
log_config_path = /path/to/log_config.ini
run_time = 07:00-23:00
time_zone = Asia/Shanghai
app_name = webhook_server_1
enable_sql_logging = false

[client_info]
# 设备标识和描述映射
device001 = 温湿度传感器1号
device002 = 温湿度传感器2号
sensor001 = 压力传感器
camera001 = 监控摄像头
gateway001 = 物联网网关
```

## 🖥️ [server] 节点配置

### 🔐 安全认证配置

#### api_key
- **类型**: string
- **必填**: ✅
- **说明**: API访问密钥，用于获取访问令牌
- **格式**: 建议使用32位以上的随机字符串
- **示例**: `api_key = AbCdEf123456789XyZ987654321`

**安全建议**:
- 使用强密码生成器生成随机密钥
- 定期更换API密钥
- 不要在代码中硬编码密钥

#### whitelist
- **类型**: string
- **必填**: ✅
- **说明**: IP白名单，控制API访问权限
- **格式**: 支持单个IP、CIDR网段、通配符
- **示例**: 
  ```ini
  # 单个IP
  whitelist = *************
  
  # 多个IP（逗号分隔）
  whitelist = *************,*************,127.0.0.1
  
  # CIDR网段
  whitelist = ***********/24,10.0.0.0/8
  
  # 允许所有IP（不推荐生产环境）
  whitelist = *
  ```

**配置建议**:
- 生产环境避免使用通配符`*`
- 使用最小权限原则，只允许必要的IP
- 定期审查和更新白名单

### 🌐 网络配置

#### host
- **类型**: string
- **必填**: ✅
- **默认值**: `0.0.0.0`
- **说明**: 服务器监听地址
- **示例**:
  ```ini
  # 监听所有网络接口（推荐）
  host = 0.0.0.0
  
  # 只监听本地回环
  host = 127.0.0.1
  
  # 监听特定网络接口
  host = *************
  ```

#### port
- **类型**: int
- **必填**: ✅
- **范围**: 1024-65535
- **默认值**: `8000`
- **说明**: 服务器监听端口
- **示例**: `port = 8000`

**端口选择建议**:
- 避免使用系统保留端口（1-1023）
- 确保端口未被其他程序占用
- 防火墙需要开放对应端口

### 💾 数据库配置

#### message_data_table_name
- **类型**: string
- **必填**: ✅
- **说明**: 数据库表名，多进程间必须唯一
- **格式**: 字母、数字、下划线组合
- **示例**: `message_data_table_name = message_data_1`

**命名建议**:
- 使用有意义的表名
- 多实例运行时确保表名唯一
- 避免使用SQL关键字

#### expire_data_days
- **类型**: int
- **必填**: ✅
- **默认值**: `7`
- **说明**: 数据过期保存天数
- **特殊值**: ≤0 表示永不过期
- **示例**:
  ```ini
  # 保存7天
  expire_data_days = 7
  
  # 保存30天
  expire_data_days = 30
  
  # 永不过期
  expire_data_days = 0
  ```

#### data_limit_num
- **类型**: int
- **必填**: ✅
- **默认值**: `100000`
- **说明**: 数据存储上限数量
- **特殊值**: ≤0 表示不限制
- **示例**:
  ```ini
  # 限制10万条
  data_limit_num = 100000
  
  # 限制100万条
  data_limit_num = 1000000
  
  # 不限制数量
  data_limit_num = 0
  ```

### 📋 系统配置

#### log_config_path
- **类型**: string
- **必填**: ✅
- **说明**: 日志配置文件路径
- **格式**: 支持相对路径和绝对路径
- **示例**:
  ```ini
  # 相对路径
  log_config_path = resources/log.ini
  
  # 绝对路径（Windows）
  log_config_path = C:\NexusRecv\config\log.ini
  
  # 绝对路径（Linux）
  log_config_path = /opt/nexusrecv/config/log.ini
  ```

#### run_time
- **类型**: string
- **必填**: ✅
- **格式**: `HH:MM-HH:MM`
- **说明**: 服务器运行时间段
- **特殊值**: 相同时间表示全天运行
- **示例**:
  ```ini
  # 工作时间运行
  run_time = 08:00-18:00
  
  # 夜间运行
  run_time = 22:00-06:00
  
  # 全天运行
  run_time = 00:00-00:00
  ```

#### time_zone
- **类型**: string
- **必填**: ✅
- **默认值**: `Asia/Shanghai`
- **说明**: 时区设置
- **格式**: 标准时区名称
- **示例**:
  ```ini
  # 中国标准时间
  time_zone = Asia/Shanghai
  
  # 美国东部时间
  time_zone = America/New_York
  
  # 欧洲中部时间
  time_zone = Europe/Berlin
  
  # UTC时间
  time_zone = UTC
  ```

#### app_name
- **类型**: string
- **必填**: ✅
- **说明**: 应用名称，多进程唯一
- **用途**: 日志文件名前缀、进程标识
- **示例**: `app_name = webhook_server_1`

**命名建议**:
- 使用有意义的应用名称
- 多实例时使用不同的名称
- 避免使用特殊字符

#### enable_sql_logging
- **类型**: bool
- **必填**: ✅
- **默认值**: `false`
- **说明**: 是否启用SQL日志记录
- **示例**:
  ```ini
  # 启用SQL日志（调试用）
  enable_sql_logging = true
  
  # 禁用SQL日志（生产环境推荐）
  enable_sql_logging = false
  ```

## 📱 [client_info] 节点配置

### 🔧 设备标识配置

`[client_info]`节点存储设备标识和描述的映射关系，用于设备认证和GUI显示。

#### 配置格式
```ini
[client_info]
设备标识1 = 设备描述1
设备标识2 = 设备描述2
```

#### 设备标识要求

| 项目 | 要求 | 说明 |
|------|------|------|
| **长度** | 10-30个字符 | 确保标识的唯一性和可读性 |
| **字符集** | 字母和数字 | 仅支持a-z, A-Z, 0-9 |
| **大小写** | 敏感 | device001与Device001是不同设备 |
| **唯一性** | 同一配置文件中不能重复 | 确保设备标识的唯一性 |

#### 设备描述要求

| 项目 | 要求 | 说明 |
|------|------|------|
| **长度** | 1-50个字符 | 便于GUI界面显示 |
| **字符集** | 中文、英文、数字、符号 | 支持多语言描述 |
| **用途** | GUI界面显示 | 帮助用户识别设备 |

#### 配置示例

```ini
[client_info]
# 传感器设备
temp001 = 温湿度传感器-办公室
temp002 = 温湿度传感器-会议室
pressure001 = 压力传感器-实验室

# 监控设备
camera001 = 监控摄像头-大门
camera002 = 监控摄像头-停车场

# 网关设备
gateway001 = 物联网网关-1楼
gateway002 = 物联网网关-2楼

# 测试设备
testdevice001 = 测试设备-开发环境
```

## 📋 完整配置示例

### 🏢 生产环境配置

```ini
# NexusRecv 生产环境配置
# 更新时间: 2025-09-03
# 维护人员: 系统管理员

[server]
# 安全配置
api_key = Prod_API_Key_2025_AbCdEf123456789XyZ
whitelist = ***********/24,10.0.0.0/8

# 网络配置
host = 0.0.0.0
port = 8000

# 数据库配置
message_data_table_name = production_messages
expire_data_days = 30
data_limit_num = 1000000

# 系统配置
log_config_path = /opt/nexusrecv/config/log.ini
run_time = 00:00-00:00
time_zone = Asia/Shanghai
app_name = nexusrecv_production
enable_sql_logging = false

[client_info]
# 生产环境设备
sensor_temp_01 = 温湿度传感器-生产车间A
sensor_temp_02 = 温湿度传感器-生产车间B
sensor_pressure_01 = 压力传感器-锅炉房
gateway_main = 主网关-数据中心
monitor_cam_01 = 监控摄像头-主入口
```

### 🧪 开发环境配置

```ini
# NexusRecv 开发环境配置
# 更新时间: 2025-09-03
# 维护人员: 开发团队

[server]
# 开发环境安全配置
api_key = Dev_API_Key_123456
whitelist = *

# 网络配置
host = 127.0.0.1
port = 8001

# 数据库配置
message_data_table_name = dev_messages
expire_data_days = 3
data_limit_num = 10000

# 系统配置
log_config_path = resources/log.ini
run_time = 00:00-00:00
time_zone = Asia/Shanghai
app_name = nexusrecv_dev
enable_sql_logging = true

[client_info]
# 开发测试设备
testdevice001 = 测试设备1
testdevice002 = 测试设备2
simulator001 = 数据模拟器
```

### 🏠 家庭环境配置

```ini
# NexusRecv 家庭环境配置
# 更新时间: 2025-09-03
# 用户: 家庭用户

[server]
# 家庭网络安全配置
api_key = Home_API_Key_Family2025
whitelist = ***********/24,***********/24

# 网络配置
host = 0.0.0.0
port = 8000

# 数据库配置
message_data_table_name = home_messages
expire_data_days = 14
data_limit_num = 50000

# 系统配置
log_config_path = resources/log.ini
run_time = 06:00-23:00
time_zone = Asia/Shanghai
app_name = nexusrecv_home
enable_sql_logging = false

[client_info]
# 家庭智能设备
thermometer_living = 客厅温湿度计
thermometer_bedroom = 卧室温湿度计
airquality_kitchen = 厨房空气质量检测器
security_door = 智能门锁
camera_garden = 花园监控摄像头
```

## ✅ 配置验证

### 🔍 自动验证

NexusRecv在启动时会自动验证配置文件，检查以下项目：

1. **📄 文件格式**: 验证INI格式是否正确
2. **📋 必填项**: 检查所有必填配置项是否存在
3. **🔢 数据类型**: 验证配置值的数据类型
4. **📏 取值范围**: 检查配置值是否在有效范围内
5. **🔗 依赖关系**: 验证配置项之间的依赖关系

### 🚨 常见验证错误

| 错误类型 | 错误信息 | 解决方案 |
|----------|----------|----------|
| **文件不存在** | `Config file not found` | 检查配置文件路径是否正确 |
| **编码错误** | `Encoding error` | 确保文件使用UTF-8编码保存 |
| **格式错误** | `Invalid INI format` | 检查INI格式语法 |
| **缺少必填项** | `Missing required field` | 添加缺少的配置项 |
| **类型错误** | `Invalid data type` | 检查配置值的数据类型 |
| **取值超范围** | `Value out of range` | 调整配置值到有效范围 |

### 🛠️ 手动验证工具

```python
# 验证配置文件的Python脚本示例
from webhook_server.config import config_check

def validate_config(config_path: str) -> bool:
    """验证配置文件是否有效"""
    try:
        config_check.common_check_config_file(config_path)
        print(f"✅ 配置文件 {config_path} 验证通过")
        return True
    except Exception as e:
        print(f"❌ 配置文件 {config_path} 验证失败: {e}")
        return False

# 使用示例
if __name__ == "__main__":
    config_files = [
        "resources/server_config.ini",
        "config/production.ini",
        "config/development.ini"
    ]
    
    for config_file in config_files:
        validate_config(config_file)
```

## 🔧 最佳实践

### 📁 文件管理

1. **📂 目录结构**: 建议将配置文件放在专门的config目录中
2. **📝 命名规范**: 使用有意义的文件名，如`production.ini`、`development.ini`
3. **🔄 版本控制**: 将配置模板纳入版本控制，实际配置文件排除
4. **🔒 权限控制**: 设置适当的文件权限，保护敏感配置

### 🔐 安全建议

1. **🔑 密钥管理**: 
   - 使用强随机密钥
   - 定期轮换API密钥
   - 不要在代码中硬编码密钥

2. **🌐 网络安全**:
   - 生产环境避免使用通配符IP
   - 使用最小权限原则配置白名单
   - 考虑使用VPN或专用网络

3. **📄 配置保护**:
   - 设置适当的文件权限（如600）
   - 定期备份配置文件
   - 使用配置管理工具

### 🚀 性能优化

1. **💾 数据管理**:
   - 合理设置数据过期时间
   - 根据实际需求设置存储上限
   - 定期清理历史数据

2. **📊 监控配置**:
   - 生产环境关闭SQL日志
   - 合理设置日志级别
   - 监控磁盘空间使用

3. **🔄 多实例配置**:
   - 确保表名和应用名唯一
   - 使用不同的端口
   - 避免资源竞争

### 🔧 故障排除

1. **📝 日志分析**: 启用详细日志，分析配置加载过程
2. **🧪 配置测试**: 使用测试环境验证配置更改
3. **🔄 逐步调试**: 从最小配置开始，逐步添加配置项
4. **📋 文档记录**: 记录配置更改历史和原因

## 📞 技术支持

如果在配置过程中遇到问题，请参考以下资源：

- **📚 快速指南**: [快速使用指南.md](快速使用指南.md)
- **🔧 故障排除**: [故障排除文档.md](故障排除文档.md)
- **👨‍💻 开发文档**: [开发者文档.md](开发者文档.md)
- **🐛 问题反馈**: [GitHub Issues](https://github.com/your-repo/nexusrecv/issues)

---

<div align="center">

**📚 相关文档**

[API接口文档](API接口文档.md) | [快速使用指南](快速使用指南.md) | [开发者文档](开发者文档.md) | [故障排除](故障排除文档.md)

</div>
