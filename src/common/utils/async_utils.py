"""异步工具模块。

此模块提供了异步编程相关的工具函数，包括：
- 安全任务执行
- 异步函数类型定义
- 异步任务调度支持
"""

import asyncio
import inspect
import logging
from typing import Any, Awaitable, Callable, Optional, Union

from common.utils import logging_utils

# 任务函数类型定义
TaskFuncType = Union[
    Callable[..., Any],
    Callable[..., Awaitable[Any]]
]


async def run_safe_task(
        task_func: TaskFuncType,
        scheduler_check: Callable[[], bool],
        task_canceled_info: str,
        task_exception_info: str,
        custom_logger: Optional[logging.Logger] = None,
        *args,
        **kwargs
) -> None:
    """定时任务通用外部嵌套函数 --- 可以传递带有参数的同步/异步函数
    
    Args:
        task_func: 要执行的任务函数（同步或异步）
        scheduler_check: 调度器检查函数
        task_canceled_info: 任务取消时的信息
        task_exception_info: 任务异常时的信息
        custom_logger: 自定义日志记录器
        *args: 任务函数的位置参数
        **kwargs: 任务函数的关键字参数
    """
    try:
        if not scheduler_check():
            return

        if inspect.iscoroutinefunction(task_func):
            await task_func(*args, **kwargs)  # 异步函数
        else:
            task_func(*args, **kwargs)        # 同步函数
    except asyncio.CancelledError:
        logging_utils.logger_print(task_canceled_info, custom_logger=custom_logger)
    except BaseException as ex:
        logging_utils.logger_print(task_exception_info, custom_logger=custom_logger, use_exception=True, exception=ex)
