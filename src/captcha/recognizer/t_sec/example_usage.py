"""滑块验证码识别器使用示例。

展示如何使用滑块验证码识别器解决各种验证码问题。
"""

import asyncio
import logging

from common.utils import logging_utils
from .captcha_solver import CaptchaSolver
from .ddddocr_client import DDDDOCRClient

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)




async def example_2_advanced_slider_captcha():
    """示例2: 高级滑块验证码解决。"""
    print("\n=== 示例2: 高级滑块验证码 ===")
    
    solver = CaptchaSolver(
        ddddocr_base_url="http://10.168.1.201:7777",
        headless=False,  # 显示浏览器窗口
        browser_type="chromium"
    )
    
    try:
        # 解决滑块验证码
        success = await solver.solve_slider_captcha_on_page(
            url="https://example.com/login",  # 替换为实际URL
            background_selector=".captcha-bg",
            slider_button_selector=".slider-btn",
            slider_selector=".slider-piece",
            success_selector=".success-msg",
            failure_selector=".error-msg",
            use_slide_comparison=True,  # 使用slideComparison API
            max_retries=5
        )
        
        if success:
            print("滑块验证码解决成功！")
            
            # 可以继续进行后续操作，比如登录
            recognizer = solver.get_slider_recognizer()
            if recognizer.page:
                # 填写用户名和密码
                await recognizer.page.fill("#username", "your_username")
                await recognizer.page.fill("#password", "your_password")
                await recognizer.page.click("#login-button")
                
                print("已尝试登录")
        else:
            print("滑块验证码解决失败")
            
    finally:
        solver.close()
    
    return success



def example_4_direct_api_usage():
    """示例4: 直接使用API客户端。"""
    print("\n=== 示例4: 直接使用API客户端 ===")
    
    client = DDDDOCRClient("http://10.168.1.201:7777")
    
    try:
        # 创建测试图片
        from PIL import Image
        import io
        
        # 创建背景图
        bg_img = Image.new('RGB', (300, 150), color='lightblue')
        bg_buffer = io.BytesIO()
        bg_img.save(bg_buffer, format='PNG')
        bg_data = bg_buffer.getvalue()
        
        # 创建滑块图
        slider_img = Image.new('RGB', (50, 50), color='red')
        slider_buffer = io.BytesIO()
        slider_img.save(slider_buffer, format='PNG')
        slider_data = slider_buffer.getvalue()
        
        print("测试各种API接口...")
        
        # 1. 测试滑块比较
        try:
            x = client.slide_comparison(slider_data, bg_data)
            print(f"滑块比较结果: x={x}")
        except Exception as e:
            print(f"滑块比较失败: {e}")

        # 2. 测试验证码识别
        try:
            x = client.capcode(slider_data, bg_data)
            print(f"验证码识别结果: x={x}")
        except Exception as e:
            print(f"验证码识别失败: {e}")
        
        # 3. 测试文字分类
        try:
            text = client.classification(bg_data)
            print(f"文字分类结果: {text}")
        except Exception as e:
            print(f"文字分类失败: {e}")
        
        # 4. 测试目标检测
        try:
            objects = client.detection(bg_data)
            print(f"目标检测结果: {len(objects)} 个对象")
            for i, bbox in enumerate(objects):
                print(f"  对象{i+1}: {bbox}")
        except Exception as e:
            print(f"目标检测失败: {e}")

        # 5. 测试数学计算
        try:
            result = client.calculate(bg_data)
            print(f"数学计算结果: {result}")
        except Exception as e:
            print(f"数学计算失败: {e}")

        # 6. 测试选择识别
        try:
            selections = client.select(bg_data)
            print(f"选择识别结果: {len(selections)} 个项目")
            for i, item in enumerate(selections):
                print(f"  项目{i+1}: {item}")
        except Exception as e:
            print(f"选择识别失败: {e}")
            
    finally:
        client.close()


async def example_5_real_world_scenario():
    """示例5: 真实场景应用。"""
    print("\n=== 示例5: 真实场景应用 ===")
    
    # 这是一个模拟真实网站登录的示例
    # 需要根据实际网站调整选择器和逻辑
    
    solver = CaptchaSolver(headless=False)
    
    try:
        recognizer = solver.get_slider_recognizer()
        
        async with recognizer:
            # 1. 导航到登录页面
            await recognizer.navigate_to_page("https://example.com/login")
            
            # 2. 填写用户名和密码
            if recognizer.page:
                await recognizer.page.fill("#username", "your_username")
                await recognizer.page.fill("#password", "your_password")
                
                # 3. 点击登录按钮，触发验证码
                await recognizer.page.click("#login-button")
                
                # 4. 等待验证码出现
                await recognizer.page.wait_for_selector(".captcha-container", timeout=10000)
                
                # 5. 解决滑块验证码
                success = await recognizer.solve_slider_captcha(
                    background_selector=".captcha-bg img",
                    slider_button_selector=".slider-handle",
                    slider_selector=".slider-piece img"
                )
                
                if success:
                    # 6. 等待登录结果
                    result = await recognizer.wait_for_captcha_result(
                        success_selector=".login-success",
                        failure_selector=".login-error"
                    )
                    
                    if result:
                        print("登录成功！")
                    else:
                        print("登录失败")
                else:
                    print("验证码解决失败")
                    
    except Exception as e:
        logging_utils.logger_print(
            msg="real world scenario failed",
            custom_logger=logger,
            use_exception=True,
            exception=e
        )
    
    finally:
        solver.close()


async def main():
    """主函数，运行所有示例。"""
    print("滑块验证码识别器使用示例")
    print("=" * 50)
    
    # 运行各个示例
    try:
        # 示例1: 简单滑块验证码（需要实际URL）
        # await example_1_simple_slider_captcha()
        
        # 示例2: 高级滑块验证码（需要实际URL）
        # await example_2_advanced_slider_captcha()
        
        # 示例3: 文字识别
        example_3_text_recognition()
        
        # 示例4: 直接使用API客户端
        example_4_direct_api_usage()
        
        # 示例5: 真实场景应用（需要实际URL）
        # await example_5_real_world_scenario()
        
    except KeyboardInterrupt:
        print("\n用户中断执行")
    except Exception as e:
        print(f"\n执行出错: {e}")
    
    print("\n示例执行完成")


if __name__ == "__main__":
    # 运行示例
    asyncio.run(main())
