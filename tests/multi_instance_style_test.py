"""测试多个TreeviewWithFixedGrid实例的样式冲突修复"""

import tkinter as tk
import ttkbootstrap as ttkb
from ttkbootstrap.constants import *

import sys
import os

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)
sys.path.insert(0, os.path.join(project_root, 'src'))

try:
    from common.models import gui_widgets
except ImportError as e:
    print(f"导入错误: {e}")
    print("请确保项目路径正确")
    sys.exit(1)


def create_treeview_instance(parent, title, row_height, odd_bg, even_bg, font_size):
    """创建一个TreeviewWithFixedGrid实例"""
    frame = ttkb.LabelFrame(parent, text=title, padding=10)
    frame.pack(fill=BOTH, expand=True, padx=5, pady=5)
    
    # 定义列
    columns = [
        {
            gui_widgets.TreeviewWithFixedGrid.COL_ID_KEY: 'col1',
            gui_widgets.TreeviewWithFixedGrid.DISPLAY_TEXT_KEY: '列1',
            gui_widgets.TreeviewWithFixedGrid.WIDTH_KEY: 120
        },
        {
            gui_widgets.TreeviewWithFixedGrid.COL_ID_KEY: 'col2',
            gui_widgets.TreeviewWithFixedGrid.DISPLAY_TEXT_KEY: '列2',
            gui_widgets.TreeviewWithFixedGrid.WIDTH_KEY: 120
        },
        {
            gui_widgets.TreeviewWithFixedGrid.COL_ID_KEY: 'col3',
            gui_widgets.TreeviewWithFixedGrid.DISPLAY_TEXT_KEY: '列3',
            gui_widgets.TreeviewWithFixedGrid.WIDTH_KEY: 120
        }
    ]
    
    # 创建TreeviewWithFixedGrid实例
    treeview = gui_widgets.TreeviewWithFixedGrid(
        parent=frame,
        columns=columns,
        font_family='Arial',
        font_size=font_size,
        need_tree=False,
        row_height=row_height,
        odd_bg=odd_bg,
        even_bg=even_bg
    )
    treeview.pack(fill=BOTH, expand=True)
    
    # 添加测试数据
    test_data = [
        {'col1': f'{title}-数据{i}', 'col2': f'内容{i}', 'col3': f'值{i}'}
        for i in range(8)
    ]
    treeview.add_rows(test_data)
    
    # 显示样式名称
    info_label = ttkb.Label(frame, text=f"样式名称: {treeview.style_name}", font=("Arial", 8))
    info_label.pack(pady=(5, 0))
    
    return treeview


def main():
    """主函数"""
    # 创建主窗口
    root = ttkb.Window(
        title="多实例样式冲突修复测试",
        themename="flatly",
        size=(900, 700)
    )
    
    # 创建主框架
    main_frame = ttkb.Frame(root)
    main_frame.pack(fill=BOTH, expand=True, padx=10, pady=10)
    
    # 创建标题
    title_label = ttkb.Label(main_frame, 
                           text="多个TreeviewWithFixedGrid实例样式独立性测试", 
                           font=("Arial", 16, "bold"))
    title_label.pack(pady=(0, 10))
    
    # 创建说明
    info_text = """
    此测试创建了4个不同配置的TreeviewWithFixedGrid实例：
    1. 实例1：行高25，白色/浅灰背景，字体大小10
    2. 实例2：行高30，浅蓝/白色背景，字体大小11  
    3. 实例3：行高35，浅绿/浅黄背景，字体大小12
    4. 实例4：行高20，浅粉/浅紫背景，字体大小9
    
    如果修复成功，每个实例应该保持其独立的样式配置，不会相互影响。
    """
    info_label = ttkb.Label(main_frame, text=info_text, font=("Arial", 9), justify=LEFT)
    info_label.pack(pady=(0, 10))
    
    # 创建容器框架
    container = ttkb.Frame(main_frame)
    container.pack(fill=BOTH, expand=True)
    
    # 创建上下两行
    top_frame = ttkb.Frame(container)
    top_frame.pack(fill=BOTH, expand=True)
    
    bottom_frame = ttkb.Frame(container)
    bottom_frame.pack(fill=BOTH, expand=True)
    
    # 创建4个不同配置的TreeviewWithFixedGrid实例
    try:
        # 实例1：标准配置
        tv1 = create_treeview_instance(
            top_frame, "实例1 (行高25)", 25, 'white', '#E8E8E8', 10
        )
        
        # 实例2：较大行高，不同背景色
        tv2 = create_treeview_instance(
            top_frame, "实例2 (行高30)", 30, '#E6F3FF', 'white', 11
        )
        
        # 实例3：更大行高，绿色系背景
        tv3 = create_treeview_instance(
            bottom_frame, "实例3 (行高35)", 35, '#E6FFE6', '#FFFACD', 12
        )
        
        # 实例4：较小行高，粉色系背景
        tv4 = create_treeview_instance(
            bottom_frame, "实例4 (行高20)", 20, '#FFE6F0', '#E6E6FA', 9
        )
        
        print("所有TreeviewWithFixedGrid实例创建成功")
        print(f"实例1样式名称: {tv1.style_name}")
        print(f"实例2样式名称: {tv2.style_name}")
        print(f"实例3样式名称: {tv3.style_name}")
        print(f"实例4样式名称: {tv4.style_name}")
        
    except Exception as e:
        print(f"创建TreeviewWithFixedGrid实例时出错: {e}")
        error_label = ttkb.Label(container, text=f"错误: {e}")
        error_label.pack()
    
    # 添加控制按钮
    button_frame = ttkb.Frame(main_frame)
    button_frame.pack(fill=X, pady=(10, 0))
    
    def refresh_all():
        """刷新所有实例的显示"""
        try:
            for widget in [tv1, tv2, tv3, tv4]:
                widget.update_separators()
        except:
            pass
    
    refresh_btn = ttkb.Button(button_frame, text="刷新显示", command=refresh_all)
    refresh_btn.pack(side=LEFT)
    
    help_label = ttkb.Label(button_frame, 
                           text="观察每个实例是否保持独立的行高和背景色配置",
                           font=("Arial", 9),
                           foreground="gray")
    help_label.pack(side=RIGHT)
    
    # 居中显示窗口
    root.update_idletasks()
    width = root.winfo_width()
    height = root.winfo_height()
    x = (root.winfo_screenwidth() // 2) - (width // 2)
    y = (root.winfo_screenheight() // 2) - (height // 2)
    root.geometry(f'{width}x{height}+{x}+{y}')
    
    root.mainloop()


if __name__ == "__main__":
    main()
