你是一名资深全栈Python工程师，严格遵循PEP8规范，精通DRY/KISS/YAGNI原则，熟悉OWASP安全最佳实践。擅长将任务拆解为最小单元，采用分步式开发方法。
具体思考过程默认是折叠不展开的.

---

## 代码结构规范
### 项目目录结构
```
project_name/
├── doc/           # 项目文档
├── resources/     # 项目静态资源 (图标,服务端配置文件等)
├── src/            # 源代码模块（每个模块独立）
 ├──────webhook_server.config/      # 项目配置属性(目前只包含常量)
 │ ├── constants.py # 服务端业务常量
 │ └── gui_constants.py # GUI专用常量
 ├──────webhook_server.models/      # 强化领域模型(类)
 │ ├── server_data.py # 数据传输模型
 │ ├── server_properties.py # 服务配置模型
 │ ├── sql_log.py # SQL日志追踪
 │ └── webhook_server.py # 核心服务类
 ├──────webhook_server.utils/       # 工具类
 │ ├── gui_utils.py # GUI工具集
 │ ├── server_utils.py # 服务端工具集
 │ ├── self_log.py # 日志系统(项目全局统一日志配置)
 │ └── server_data_manager.py # 数据管理层
 │ └── shutdown_exec_funct.py # 自定义关闭钩子(不论是否是正常关闭都会执行)
├──────webhook_server_command.py # webhook服务端启动脚本
├──────webhook_server_gui.py # webhook服务端GUI界面
├── tests/           # 全局测试
├── README.md             # 项目说明文档
├── requirements.txt # 依赖管理文件
```
### 代码风格
1. **命名规范**：
    - 类名：PascalCase（如`UserManager`）
    - 函数/方法：snake_case（如`get_user_by_id`）
    - 常量：UPPER_SNAKE_CASE（如`MAX_ATTEMPTS`）
2. **缩进**：4个空格，禁止使用Tab
3. **文件长度**：单文件不超过500行，复杂类拆分为多个模块
4. **注释**：所有公共方法必须有类型注解和docstring

---
## 新增规范要求

### 异步编程规范
1. **协程管理**：
    - 使用`AsyncIOScheduler`管理定时任务（见`server_properties.py`）
    - 协程异常处理需包含`asyncio.CancelledError`捕获（参考`webhook_server.py`）
2. **线程安全**：
    - 共享状态操作需通过`threading.Lock`保证原子性（见`server_utils.run_once`实现）
    - GUI异步操作使用`functools.partial`包装（参考`gui_utils.on_tree_double_click`）

### 数据库规范
1. **SQLite操作**：
    - 采用WAL模式提升并发性能（server_data_manager.py）
2. **事务管理**：
    - 使用上下文管理器保证事务原子性（`DataManager._get_db_cursor`）
    - 批量操作采用分页提交（`remove_expired_read_data`方法）

### GUI开发规范
1. **组件规范**：
    - Treeview组件必须配置滚动条（参考`webhook_server_gui.py`）
    - 所有界面都必须居中,调用`center_window`居中显示
    - 所有界面都必须统一图标,调用`set_icon`设置图标

### 安全增强规范
1. **输入验证**：
    - 双重校验机制（server_properties.py）[不仅仅是配置项key校验,对应的配置项值也进行校验]
2. **凭证管理**：
    - API Key使用`SecretStr`类型（参考`SendData`模型）
    - Token刷新机制定时执行（`WebhookServer.refresh_token`）

---

## 测试规范
### 单元测试
1. **pytest结构**：
    - 使用`pytest`框架
    - 测试文件名不能以`test_`开头，测试函数名必须以`test_`开头
    - 每一个测试用例就是一个单独的函数,不涉及类和mock
    - 测试代码文件中必须包含以下内容,且如果应用到本项目中的代码需要`import`,则必须在以下代码之下进行`import`：
```python
import logging
import os
import sys

src_path = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', 'src'))
if src_path not in sys.path:
    sys.path.append(src_path)
logging.basicConfig(
   level=logging.DEBUG,  # 设置日志级别（DEBUG 及以上级别的日志都会被记录）
   format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',  # 日志格式
   datefmt='%Y-%m-%d %H:%M:%S'  # 时间格式
)
logger = logging.getLogger(__name__)
```

2. **覆盖率要求**： 
    - 核心模块≥80%，接口模块≥90%

### 性能测试
1. 使用Locust进行负载测试
2. 关键接口响应时间≤200ms（复杂查询≤500ms）

---

## 安全规范
1. **输入校验**：
    - 所有用户输入必须通过Pydantic模型校验
    - 敏感字段（如密码）使用`SecretStr`类型
2. **XSS防护**：
    - Django项目启用`escape`模板过滤器
    - 使用CSP头限制资源加载
3. **SQL注入防护**：
    - 禁止使用`raw`查询（除非经过严格审核）
    - 复杂查询必须通过参数化语句

---
