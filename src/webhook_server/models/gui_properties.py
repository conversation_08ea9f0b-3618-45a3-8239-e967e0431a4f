"""GUI属性管理类。

统一管理Webhook服务器GUI的所有属性、常量和配置项。
"""
import logging
import subprocess
import sys
import tkinter
from typing import Optional, Union, Any
from zoneinfo import ZoneInfo

import ttkbootstrap as ttkb

from common.constants import const
from common.utils import process_monitor, logging_utils, gui_utils
from webhook_server.config import gui_constants, constants
from webhook_server.models import server_data_manager
from webhook_server.utils import config_lock, webhook_server_utils


class GUIProperties:
    """GUI属性管理类。
    
    统一管理所有GUI相关的属性、常量和配置项，避免属性分散在各个组件中。
    一般存放多个gui组件都需要访问的属性、常量和配置项
    """
    
    # 类常量
    NO_DATA_SHOW_TAG = 'no_data_show'  # 在没有数据时实时数据表格中行的标签
    
    def __init__(self, server_config_path: str):
        self.logger: Optional[logging.Logger] = None
        """初始化GUI属性。"""
        logging_utils.logger_print(msg="initializing webhook server gui", custom_logger=self.logger)
        # 配置管理器 参数值是gui界面生成配置文件的配置项的值,两者统一
        self.config_manager = config_lock.MultiProcessConfigManager.get_singleton_instance(db_path=constants.CROSS_PROCESS_DATA_BASE_PATH, enable_sql_logging=gui_constants.ENABLE_SQL_LOGGING, zone=const.DEFAULT_TIMEZONE)
        # 服务端配置文件路径
        logging_utils.logger_print(msg="setting up server config path", custom_logger=self.logger)
        self.validate_config_file_path(server_config_path)
        self.config_file_path = server_config_path
        self.config_file_time_zone:Optional[ZoneInfo] = None
        logging_utils.logger_print(msg=f"using provided config path: {server_config_path}", custom_logger=self.logger)

        # 该值是使用配置文件加载到该变量上,其键值对和实际配置文件是完全一致的,方便直接使用,所有的变量的值都是字符串类型
        self.server_config_values:dict[str,Any] = {}
        # 该变量和client_tree一起使用
        self.client_info_entries = {} #实时客户端设备标识界面信息
        self.file_client_info_entries={} # 实际配置文件中客户端设备标识信息
        self.log_config_path:Optional[str] = None
        self.error_occured = False
        self.message_data_manager: Optional[server_data_manager.WebhookDataManager] = None

        self.notification_bar_text:str = gui_constants.DEFAULT_NOTIFICATION_MESSAGE

        # gui字体
        self.ui_font_family:Optional[str]=None
        self.server_total_uptime_var:Optional[ttkb.StringVar]=None
        self.server_pid_var:Optional[ttkb.StringVar]=None
        self.server_memory_var:Optional[ttkb.StringVar]=None
        # 服务端进程
        self.server_process:Optional[subprocess.Popen]=None

        # 服务端进程监控器，专门监控server_process
        self.server_monitor:Optional[process_monitor.ProcessMonitor]=None
        # 主窗口
        self.root:Optional[ttkb.Window]=None

    def validate_config_file_path(self,server_config_path:str):
        """在初始化时,校验配置文件路径所对应的配置文件是否符合要求"""
        try:
            self.config_manager.main_gui_load_config(server_config_path)
        except Exception as e:
            webhook_server_utils.write_error_to_temp_file(str(e))
            sys.exit(1)

    def server_process_alive(self)->bool:
        """判断服务端进程是否存活"""
        return hasattr(self, "server_process") and self.server_process is not None and self.server_process.poll() is None

    def show_operation_error(self, error, error_msg, parent_gui:Optional[Union[tkinter.Tk,tkinter.Toplevel]], no_exit=True):
        """集中处理用户操作错误"""
        self.error_occured = True
        gui_utils.handle_code_error(error=error, error_msg=error_msg, parent_gui=parent_gui, no_exit=no_exit)
