import re
from typing import Optional

def extract_verification_code(text: str) -> Optional[str]:
    """
    从短信文本中提取验证码（支持中英文）。
    - 如果输入为空或全空白，抛 ValueError。
    - 返回纯数字的验证码字符串（去掉空格、连字符等）。若未找到返回 None。
    """
    if not isinstance(text, str) or not text.strip():
        raise ValueError("输入参数不能为空")

    # 优先级模式列表（从高到低）
    patterns = [
        # 中文上下文常见说法
        r'(?:您的.*?验证码|您的.*?校验码|您的验证码是|您的验证码为|验证码为|验证码：|验证码:|短信验证码|注册验证码|登录验证码|动态验证码|校验码)(?:\s|[:：,，。]*)?([A-Za-z0-9\-\s]{3,12})',
        # 英文上下文常见说法
        r'(?:verification code|your verification code is|your verification code|verification code:|code:|code is|your otp is|otp:|otp is|one-time code|security code|pin is|one-time pin|captcha is|your code is)(?:\s|[:：,，。]*)?([A-Za-z0-9\-\s]{3,12})',
        # 有时写在前面的 "[Google] 762114 is your ...", "8415 is your Amazon OTP."
        r'([A-Za-z0-9\-]{3,12})\s+(?:is\s+your|is\s+the|is\s+for|is\s+the\s+code|is\s+your\s+otp)',
        # 特殊带前缀的如 G-350333
        r'([A-Za-z]-\d{4,6})'
    ]

    # 检查每个模式，提取并规范化（只保留数字）
    for pat in patterns:
        for m in re.finditer(pat, text, flags=re.IGNORECASE):
            token = m.group(1)
            if not token:
                continue
            digits = re.sub(r'\D', '', token)  # 去掉非数字
            if 4 <= len(digits) <= 8:
                return digits

    # 回退策略1：直接找 4-8 位独立数字串（避免长电话号码）
    m = re.search(r'(?<!\d)(\d{4,8})(?!\d)', text)
    if m:
        return m.group(1)

    # 回退策略2：找带中划线或空格的短码，例如 182-017 / 182 017
    m = re.search(r'(?<!\d)(\d{2,3}[-\s]\d{3,4})(?!\d)', text)
    if m:
        digits = re.sub(r'\D', '', m.group(1))
        if 4 <= len(digits) <= 8:
            return digits

    return None
