# 🔌 NexusRecv API接口文档

[![API Version](https://img.shields.io/badge/API-v1.0-blue.svg)](README.md)
[![Framework](https://img.shields.io/badge/framework-FastAPI-009688.svg)](https://fastapi.tiangolo.com/)
[![Format](https://img.shields.io/badge/format-JSON-orange.svg)](README.md)
[![Auth](https://img.shields.io/badge/auth-Bearer%20Token-green.svg)](README.md)

## 📋 概述

NexusRecv提供基于FastAPI的高性能RESTful API接口，支持设备数据的接收、存储和查询。所有接口采用JSON格式进行数据交换，支持异步处理和自动文档生成。

### 🎯 API特性
- **🚀 高性能**: 基于FastAPI的异步处理，支持高并发
- **📚 自动文档**: 提供Swagger UI和ReDoc交互式文档
- **🔒 安全认证**: 多层认证机制，确保数据安全
- **✅ 数据验证**: 基于Pydantic的自动数据验证
- **📊 实时监控**: 支持实时数据查询和状态监控

## 🌐 基础信息

### 📊 服务信息

| 项目 | 说明 | 示例 |
|------|------|------|
| **基础URL** | 服务器地址和端口 | `http://*************:8000` |
| **数据格式** | 请求和响应格式 | `application/json` |
| **字符编码** | 数据编码格式 | `UTF-8` |
| **API版本** | 当前API版本 | `v1.0` |

### 🔐 认证方式

| 认证类型 | 使用场景 | Header格式 | 说明 |
|----------|----------|------------|------|
| **API Key** | 获取访问令牌 | `Authorization: Bearer {api_key}` | 管理员级别认证 |
| **Bearer Token** | 数据查询操作 | `Authorization: Bearer {token}` | 临时访问令牌 |
| **Client Key** | 数据提交操作 | `X-Client-Key: {client_key}` | 设备标识认证 |

### 📚 交互式文档

| 文档类型 | 访问地址 | 说明 |
|----------|----------|------|
| **Swagger UI** | `http://{host}:{port}/docs` | 交互式API测试界面 |
| **ReDoc** | `http://{host}:{port}/redoc` | 美观的API文档界面 |
| **OpenAPI Schema** | `http://{host}:{port}/openapi.json` | API规范JSON文件 |

## 🛠️ 接口详情

### 🔑 1. 获取访问令牌

**接口信息**:
- **路径**: `GET /webhook/token`
- **功能**: 使用API密钥获取访问令牌
- **认证**: Bearer Token (API Key)
- **IP白名单**: 是

**请求参数**:
- **Headers**:
  - `Authorization`: Bearer {api_key} (必填)

**请求示例**:
```bash
curl -X GET "http://*************:8000/webhook/token" \
     -H "Authorization: Bearer your_api_key_here"
```

**响应示例**:
```json
{
  "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
}
```

**响应字段说明**:
| 字段 | 类型 | 说明 |
|------|------|------|
| `token` | string | 访问令牌，用于后续API调用 |

**错误响应**:
```json
{
  "detail": "Invalid API key"
}
```

### 💾 2. 保存设备数据

**接口信息**:
- **路径**: `POST /webhook/save`
- **功能**: 接收并存储来自设备的数据
- **认证**: X-Client-Key Header
- **IP白名单**: 否

**请求参数**:
- **Headers**:
  - `Content-Type`: application/json (必填)
  - `X-Client-Key`: {client_key} (必填)
- **Body**:
  - `content`: string (必填，1-100字符)

**请求示例**:
```bash
curl -X POST "http://*************:8000/webhook/save" \
     -H "Content-Type: application/json" \
     -H "X-Client-Key: device001" \
     -d '{"content":"温度:25.6°C,湿度:60%"}'
```

**请求体格式**:
```json
{
  "content": "温度:25.6°C,湿度:60%"
}
```

**响应示例**:
```json
{
  "status": "success",
  "message_id": "01HZXYZ123456789ABCDEF"
}
```

**响应字段说明**:
| 字段 | 类型 | 说明 |
|------|------|------|
| `status` | string | 操作状态，成功为"success" |
| `message_id` | string | 消息唯一标识符(ULID格式) |

**错误响应**:
```json
{
  "detail": "Client key not found in configuration"
}
```

### 📤 3. 获取未读数据

**接口信息**:
- **路径**: `GET /webhook/unread`
- **功能**: 获取未读的设备数据
- **认证**: Bearer Token
- **IP白名单**: 是

**请求参数**:
- **Headers**:
  - `Authorization`: Bearer {token} (必填)
- **Query Parameters**:
  - `size`: int (必填，返回数据数量，必须>0)
  - `client_key`: string (可选，设备标识过滤)
  - `minutes`: int (可选，时间范围过滤，单位分钟)

**请求示例**:
```bash
curl -X GET "http://*************:8000/webhook/unread?size=10&client_key=device001" \
     -H "Authorization: Bearer your_token_here"
```

**响应示例**:
```json
{
  "messages": [
    {
      "id": "01HZXYZ123456789ABCDEF",
      "message": "温度:25.6°C,湿度:60%",
      "client_key": "device001",
      "reception_time": "2025-09-03 10:30:45"
    },
    {
      "id": "01HZXYZ987654321FEDCBA",
      "message": "压力:1013.25hPa",
      "client_key": "sensor001",
      "reception_time": "2025-09-03 10:29:30"
    }
  ]
}
```

**响应字段说明**:
| 字段 | 类型 | 说明 |
|------|------|------|
| `messages` | array | 消息列表 |
| `messages[].id` | string | 消息唯一标识符 |
| `messages[].message` | string | 消息内容 |
| `messages[].client_key` | string | 设备标识 |
| `messages[].reception_time` | string | 接收时间(本地时区) |

**错误响应**:
```json
{
  "detail": "Invalid or expired token"
}
```

## 🚨 错误处理

### 📋 HTTP状态码

| 状态码 | 说明 | 常见原因 |
|--------|------|----------|
| **200** | 请求成功 | 操作正常完成 |
| **400** | 请求错误 | 参数格式错误、缺少必填参数 |
| **401** | 认证失败 | API密钥无效、令牌过期 |
| **403** | 权限不足 | IP不在白名单、设备未配置 |
| **404** | 资源不存在 | 接口路径错误 |
| **422** | 数据验证失败 | 数据格式不符合要求 |
| **500** | 服务器错误 | 内部服务器错误 |

### ⚠️ 错误响应格式

**标准错误响应**:
```json
{
  "detail": "错误描述信息"
}
```

**验证错误响应**:
```json
{
  "detail": [
    {
      "loc": ["body", "content"],
      "msg": "field required",
      "type": "value_error.missing"
    }
  ]
}
```

### 🔧 常见错误及解决方案

| 错误信息 | 原因 | 解决方案 |
|----------|------|----------|
| `Invalid API key` | API密钥错误 | 检查配置文件中的api_key设置 |
| `Client key not found` | 设备标识未配置 | 在client_info中添加设备标识 |
| `Token expired` | 访问令牌过期 | 重新获取访问令牌 |
| `IP not in whitelist` | IP不在白名单 | 检查whitelist配置 |
| `Content too long` | 数据内容过长 | 确保content字段≤100字符 |

## 💡 使用示例

### 🐍 Python示例

```python
import requests
import json

class NexusRecvClient:
    def __init__(self, base_url: str, api_key: str):
        self.base_url = base_url
        self.api_key = api_key
        self.token = None
    
    def get_token(self) -> str:
        """获取访问令牌"""
        response = requests.get(
            f"{self.base_url}/webhook/token",
            headers={"Authorization": f"Bearer {self.api_key}"}
        )
        response.raise_for_status()
        self.token = response.json()["token"]
        return self.token
    
    def send_data(self, client_key: str, content: str) -> str:
        """发送设备数据"""
        response = requests.post(
            f"{self.base_url}/webhook/save",
            json={"content": content},
            headers={"X-Client-Key": client_key}
        )
        response.raise_for_status()
        return response.json()["message_id"]
    
    def get_unread_data(self, size: int, client_key: str = None) -> list:
        """获取未读数据"""
        if not self.token:
            self.get_token()
        
        params = {"size": size}
        if client_key:
            params["client_key"] = client_key
        
        response = requests.get(
            f"{self.base_url}/webhook/unread",
            params=params,
            headers={"Authorization": f"Bearer {self.token}"}
        )
        response.raise_for_status()
        return response.json()["messages"]

# 使用示例
client = NexusRecvClient("http://*************:8000", "your_api_key")

# 发送数据
message_id = client.send_data("device001", "温度:25.6°C")
print(f"数据已发送，消息ID: {message_id}")

# 获取数据
messages = client.get_unread_data(10, "device001")
print(f"获取到 {len(messages)} 条未读消息")
```

### 🌐 JavaScript示例

```javascript
class NexusRecvClient {
    constructor(baseUrl, apiKey) {
        this.baseUrl = baseUrl;
        this.apiKey = apiKey;
        this.token = null;
    }
    
    async getToken() {
        const response = await fetch(`${this.baseUrl}/webhook/token`, {
            headers: {
                'Authorization': `Bearer ${this.apiKey}`
            }
        });
        
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        
        const data = await response.json();
        this.token = data.token;
        return this.token;
    }
    
    async sendData(clientKey, content) {
        const response = await fetch(`${this.baseUrl}/webhook/save`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Client-Key': clientKey
            },
            body: JSON.stringify({ content })
        });
        
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        
        const data = await response.json();
        return data.message_id;
    }
    
    async getUnreadData(size, clientKey = null) {
        if (!this.token) {
            await this.getToken();
        }
        
        const params = new URLSearchParams({ size: size.toString() });
        if (clientKey) {
            params.append('client_key', clientKey);
        }
        
        const response = await fetch(`${this.baseUrl}/webhook/unread?${params}`, {
            headers: {
                'Authorization': `Bearer ${this.token}`
            }
        });
        
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        
        const data = await response.json();
        return data.messages;
    }
}

// 使用示例
const client = new NexusRecvClient('http://*************:8000', 'your_api_key');

// 发送数据
client.sendData('device001', '温度:25.6°C')
    .then(messageId => console.log(`数据已发送，消息ID: ${messageId}`))
    .catch(error => console.error('发送失败:', error));

// 获取数据
client.getUnreadData(10, 'device001')
    .then(messages => console.log(`获取到 ${messages.length} 条未读消息`))
    .catch(error => console.error('获取失败:', error));
```

## 🔧 最佳实践

### 📊 性能优化

1. **🔄 连接复用**: 使用HTTP连接池，避免频繁建立连接
2. **📦 批量处理**: 合理控制请求频率，避免过于频繁的API调用
3. **⏱️ 超时设置**: 设置合理的请求超时时间，避免长时间等待
4. **🔁 重试机制**: 实现指数退避的重试机制，处理临时网络问题

### 🔒 安全建议

1. **🔐 密钥管理**: 妥善保管API密钥，不要在代码中硬编码
2. **🌐 HTTPS使用**: 生产环境建议使用HTTPS加密传输
3. **⏰ 令牌刷新**: 定期刷新访问令牌，避免令牌过期
4. **📝 日志记录**: 记录API调用日志，便于问题排查

### 🚨 错误处理

1. **📋 状态码检查**: 始终检查HTTP状态码，正确处理各种错误情况
2. **🔁 重试逻辑**: 对于临时性错误（如网络超时），实现重试机制
3. **📝 错误日志**: 记录详细的错误信息，便于问题定位
4. **👥 用户友好**: 向用户提供友好的错误提示信息

## 📞 技术支持

如果在使用API过程中遇到问题，请通过以下方式获取帮助：

- **📧 邮箱支持**: <EMAIL>
- **🐛 问题反馈**: [GitHub Issues](https://github.com/your-repo/nexusrecv/issues)
- **📚 文档中心**: [项目文档](../README.md)
- **💬 社区讨论**: [GitHub Discussions](https://github.com/your-repo/nexusrecv/discussions)

---

<div align="center">

**📚 更多文档**

[快速开始](../README.md) | [配置说明](配置文件说明.md) | [开发指南](开发者文档.md) | [故障排除](故障排除文档.md)

</div>
