# 测试BaseConfigUniquenessManager.get_singleton_instance的唯一性
import logging
import os
import threading
from typing import Any, Dict, List, TypeVar, Type

from tests.discard.runtime_webhook_server_config_lock import RuntimeWebhookConfigManager

from common.models.base_config_unique_manage import BaseConfigUniquenessManager

logger = logging.getLogger(__name__)

class TestConfigManager(BaseConfigUniquenessManager):
    """测试用配置管理器子类，实现基类抽象方法"""
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self._registered_configs: Dict[str, Any] = {}
        self._lock = threading.Lock()

    def _init_db(self):
        # 测试实现不需要实际初始化数据库
        pass
    def _cleanup_current_process(self):
        with self._lock:
            self._registered_configs.clear()
            logger.info("TestConfigManager process cleanup completed")
            
    def _heartbeat_worker(self):
        while not self._stop_heartbeat_event.wait(self._heartbeat_interval):
            with self._lock:
                logger.debug("Heartbeat running in TestConfigManager")
                
    def _cleanup_inactive_records(self, cur):
        # 测试实现不需要实际清理数据库
        pass
        
    def register_config(self, key: str, value: Any) -> bool:
        with self._lock:
            if key in self._registered_configs:
                return False
            self._registered_configs[key] = value
            return True
            
    def unregister_config(self, key: str) -> bool:
        with self._lock:
            if key not in self._registered_configs:
                return False
            del self._registered_configs[key]
            return True
            
    def update_config(self, key: str, value: Any) -> bool:
        with self._lock:
            if key not in self._registered_configs:
                return False
            self._registered_configs[key] = value
            return True
            
    def get_valid_values_for_key(self, config_key: str) -> Any:
        with self._lock:
            return self._registered_configs.get(config_key)
            
    def check_config_registrable(self, key: str) -> bool:
        with self._lock:
            return key not in self._registered_configs
# 定义泛型类型变量 T，约束为 BaseConfigUniquenessManager 或其子类
T = TypeVar('T', bound='BaseConfigUniquenessManager')
def  get_singleton_instance_multithread_test(cls: Type[T]):
    """验证多线程环境下获取的实例是单例"""
    instances: List[cls] = []
    lock = threading.Lock()
    errors: List[Exception] = []

    def worker():
        try:
            # 获取单例实例
            instance = cls.get_singleton_instance()  # noqa
            with lock:
                instances.append(instance)
        except Exception as e:
            with lock:
                errors.append(e)

    # 创建并启动多个线程
    threads = []
    for i in range(10):
        t = threading.Thread(target=worker, name=f"Worker-{i}")
        threads.append(t)
        t.start()

    # 等待所有线程完成
    for t in threads:
        t.join()

    # 验证结果
    assert not errors, f"Errors occurred: {errors}"

    # 检查所有实例是否相同
    first_instance = instances[0]
    for instance in instances[1:]:
        assert instance is first_instance, "All instances should be the same singleton object"

    # 验证单例属性一致性
    assert first_instance._pid == os.getpid(), "Singleton should have the same PID as main process"  # noqa


if __name__ == '__main__':
    # 测试单例实例获取
    try:
        t1=TestConfigManager()
    except Exception as e:
        error_msg=str(e)
        if "get_singleton_instance" in error_msg:
            logger.info("TestConfigManager instance creation failed as expected")
        else:
            logger.error(f"TestConfigManager instance creation failed: {e}")
    get_singleton_instance_multithread_test(TestConfigManager)
    get_singleton_instance_multithread_test(RuntimeWebhookConfigManager)
    RuntimeWebhookConfigManager.get_singleton_instance().register_config("test_key", "test_value")
    RuntimeWebhookConfigManager.get_singleton_instance().cleanup_current_process()
