"""测试TreeviewWithFixedGrid背景色修复效果的测试文件"""

import tkinter as tk
import ttkbootstrap as ttkb
from ttkbootstrap.constants import *

import sys
import os

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)
sys.path.insert(0, os.path.join(project_root, 'src'))

from common.models import gui_widgets


def create_test_window():
    """创建测试窗口"""
    root = ttkb.Window(
        title="TreeviewWithFixedGrid背景色修复测试",
        themename="flatly",
        size=(800, 600),
        resizable=(True, True)
    )
    
    # 创建主框架
    main_frame = ttkb.Frame(root)
    main_frame.pack(fill=BOTH, expand=True, padx=10, pady=10)
    
    # 创建标题
    title_label = ttkb.Label(main_frame, text="背景色修复测试", font=("Arial", 16, "bold"))
    title_label.pack(pady=(0, 10))
    
    # 创建说明
    info_label = ttkb.Label(main_frame, 
                           text="观察表格中的背景色是否严格限制在行高范围内，不应该超出行的边界",
                           font=("Arial", 10))
    info_label.pack(pady=(0, 10))
    
    # 创建表格容器
    table_frame = ttkb.Frame(main_frame)
    table_frame.pack(fill=BOTH, expand=True)
    
    # 定义列
    columns = [
        {
            gui_widgets.TreeviewWithFixedGrid.COL_ID_KEY: 'data_content',
            gui_widgets.TreeviewWithFixedGrid.DISPLAY_TEXT_KEY: '数据内容[复制-复制完整数据内容]',
            gui_widgets.TreeviewWithFixedGrid.WIDTH_KEY: 300
        },
        {
            gui_widgets.TreeviewWithFixedGrid.COL_ID_KEY: 'receive_time',
            gui_widgets.TreeviewWithFixedGrid.DISPLAY_TEXT_KEY: '接收时间',
            gui_widgets.TreeviewWithFixedGrid.WIDTH_KEY: 150
        },
        {
            gui_widgets.TreeviewWithFixedGrid.COL_ID_KEY: 'device_id',
            gui_widgets.TreeviewWithFixedGrid.DISPLAY_TEXT_KEY: '设备标识',
            gui_widgets.TreeviewWithFixedGrid.WIDTH_KEY: 120
        },
        {
            gui_widgets.TreeviewWithFixedGrid.COL_ID_KEY: 'read_status',
            gui_widgets.TreeviewWithFixedGrid.DISPLAY_TEXT_KEY: '是否被其他设备读取',
            gui_widgets.TreeviewWithFixedGrid.WIDTH_KEY: 180
        }
    ]
    
    # 创建TreeviewWithFixedGrid
    treeview = gui_widgets.TreeviewWithFixedGrid(
        parent=table_frame,
        columns=columns,
        font_family='Arial',
        font_size=11,
        need_tree=False,
        row_height=30,  # 设置较大的行高以便观察背景色效果
        odd_bg='white',
        even_bg='#F0F0F0'  # 浅灰色背景
    )
    treeview.pack(side=LEFT, fill=BOTH, expand=True)
    
    # 添加滚动条
    scrollbar = ttkb.Scrollbar(table_frame, orient="vertical", command=treeview.yview)
    scrollbar.pack(side=RIGHT, fill=Y)
    treeview.configure(yscrollcommand=scrollbar.set)
    
    # 添加测试数据
    test_data = []
    for i in range(20):
        test_data.append({
            'data_content': f'测试数据-{i+1}',
            'receive_time': f'2025-09-03 11:16:{10+i:02d}',
            'device_id': f'1234567890',
            'read_status': '未被读取' if i % 3 == 0 else '已被其他设备读取数据'
        })
    
    treeview.add_rows(test_data)
    
    # 创建控制按钮框架
    button_frame = ttkb.Frame(main_frame)
    button_frame.pack(fill=X, pady=(10, 0))
    
    def add_more_data():
        """添加更多数据"""
        current_count = len(treeview.get_children())
        new_data = []
        for i in range(5):
            new_data.append({
                'data_content': f'新增数据-{current_count + i + 1}',
                'receive_time': f'2025-09-03 12:00:{i:02d}',
                'device_id': f'9876543210',
                'read_status': '新增状态'
            })
        treeview.add_rows(new_data)
    
    def clear_data():
        """清除所有数据"""
        treeview.clear_all_data()
    
    def change_row_height():
        """改变行高测试"""
        new_height = 40 if treeview.row_height == 30 else 30
        treeview.row_height = new_height
        # 重新配置样式
        treeview._set_style()
        treeview._configure_row_tags()
        # 刷新显示
        treeview._refresh_row_display()
        treeview.update_separators()
    
    # 添加控制按钮
    add_btn = ttkb.Button(button_frame, text="添加数据", command=add_more_data)
    add_btn.pack(side=LEFT, padx=(0, 5))
    
    clear_btn = ttkb.Button(button_frame, text="清除数据", command=clear_data)
    clear_btn.pack(side=LEFT, padx=(0, 5))
    
    height_btn = ttkb.Button(button_frame, text="切换行高", command=change_row_height)
    height_btn.pack(side=LEFT, padx=(0, 5))
    
    # 添加说明标签
    help_label = ttkb.Label(button_frame, 
                           text="使用这些按钮测试不同情况下背景色的显示效果",
                           font=("Arial", 9),
                           foreground="gray")
    help_label.pack(side=RIGHT)
    
    return root


def main():
    """主函数"""
    root = create_test_window()
    
    # 居中显示窗口
    root.update_idletasks()
    width = root.winfo_width()
    height = root.winfo_height()
    x = (root.winfo_screenwidth() // 2) - (width // 2)
    y = (root.winfo_screenheight() // 2) - (height // 2)
    root.geometry(f'{width}x{height}+{x}+{y}')
    
    root.mainloop()


if __name__ == "__main__":
    main()
