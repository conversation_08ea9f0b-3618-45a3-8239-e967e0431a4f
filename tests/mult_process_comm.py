import asyncio
import multiprocessing
import signal


class AsyncProcessController:
    """管理异步子进程的启动和优雅关闭"""

    def __init__(self):
        self.shutdown_event = multiprocessing.Event()
        self.process = None

    def start_child_process(self, async_func, *args, **kwargs):
        """启动运行异步函数的子进程"""
        self.process = multiprocessing.Process(
            target=self._run_async_in_process,
            args=(async_func, self.shutdown_event, args, kwargs)
        )
        self.process.daemon = True  # 确保子进程随父进程退出
        self.process.start()
        print(f"[Parent] Started child process PID: {self.process.pid}")
        return self.process

    def signal_shutdown(self):
        """通知子进程关闭"""
        if self.process and self.process.is_alive():
            print("\n[Parent] Sending shutdown signal...")
            self.shutdown_event.set()

    def wait_for_exit(self, timeout=5):
        """等待子进程退出"""
        if self.process:
            self.process.join(timeout)
            if self.process.is_alive():
                print("[WARNING] Child process did not exit gracefully!")
                self.process.terminate()  # 强制终止
            else:
                print("[Parent] Child process exited cleanly")

    @staticmethod
    def _run_async_in_process(async_func, shutdown_event, args, kwargs):
        """子进程入口点 - 运行异步函数并处理关闭事件"""
        # 设置子进程忽略键盘中断 (Ctrl+C)
        signal.signal(signal.SIGINT, signal.SIG_IGN)

        # 创建新的事件循环
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)

        try:
            # 创建异步任务
            main_task = loop.create_task(async_func(*args, **kwargs))

            # 添加关闭事件检查任务
            shutdown_check_task = loop.create_task(
                AsyncProcessController._wait_for_shutdown(shutdown_event)
            )

            # 同时运行主任务和关闭监控
            done, pending = loop.run_until_complete(asyncio.wait(
                [main_task, shutdown_check_task],
                return_when=asyncio.FIRST_COMPLETED
            ))

            # 处理关闭事件
            if shutdown_check_task in done and shutdown_event.is_set():
                print("\n[Child] Shutdown signal received!")
                if not main_task.done():
                    print("[Child] Canceling main task...")
                    main_task.cancel()

                # 给任务一个完成清理的机会
                loop.run_until_complete(asyncio.sleep(0.1))

            # 执行最终清理
            if pending:
                for task in pending:
                    task.cancel()

                # 收集被取消的任务
                loop.run_until_complete(asyncio.gather(*pending, return_exceptions=True))

            print("[Child] All tasks cleaned up")
        except Exception as e:
            print(f"[Child] Error in async process: {str(e)}")
        finally:
            # 关闭事件循环
            if not loop.is_closed():
                loop.close()
            print("[Child] Process exiting")

    @staticmethod
    async def _wait_for_shutdown(shutdown_event):
        """异步等待关闭事件"""
        while not shutdown_event.is_set():
            await asyncio.sleep(0.5)  # 每0.5秒检查一次
        print("[Child] Shutdown event detected in async handler")

# 示例使用
async def child_worker(worker_id, interval=1):
    """子进程的异步工作函数"""
    try:
        print(f"[Child-{worker_id}] Starting work...")
        counter = 0
        while True:
            print(f"[Child-{worker_id}] Working {counter}...")
            counter += 1
            # 模拟异步工作
            await asyncio.sleep(interval)
    except asyncio.CancelledError:
        print(f"[Child-{worker_id}] Cleanup in progress...")
        # 模拟清理操作
        await asyncio.sleep(1.5)
        print(f"[Child-{worker_id}] Cleanup completed!")

if __name__ == "__main__":
    controller = AsyncProcessController()

    # 启动子进程 - 传递函数和参数而不是协程对象
    controller.start_child_process(child_worker, "Worker-1", 1.2)

    try:
        # 主进程继续工作
        print("[Parent] Main process running. Press Ctrl+C to initiate shutdown...")
        print("[Parent] You can also type 'exit' to initiate shutdown")

        # 简单的用户输入控制
        while True:
            user_input = input("> ").strip().lower()
            if user_input in ['exit', 'quit']:
                break
            print(f"[Parent] Received input: {user_input}")
    except KeyboardInterrupt:
        print("\n[Parent] Received Ctrl+C")

    # 发送关闭信号
    controller.signal_shutdown()

    # 等待子进程退出
    controller.wait_for_exit()
    print("[Parent] System shutdown complete")
