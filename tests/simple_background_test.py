"""简化的TreeviewWithFixedGrid背景色测试"""

import tkinter as tk
import ttkbootstrap as ttkb
from ttkbootstrap.constants import *

import sys
import os

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)
sys.path.insert(0, os.path.join(project_root, 'src'))

try:
    from common.models import gui_widgets
except ImportError as e:
    print(f"导入错误: {e}")
    print("请确保项目路径正确")
    sys.exit(1)


def main():
    """主函数"""
    # 创建简单的测试窗口
    root = ttkb.Window(
        title="背景色修复测试",
        themename="flatly",
        size=(600, 400)
    )
    
    # 创建主框架
    frame = ttkb.Frame(root)
    frame.pack(fill=BOTH, expand=True, padx=10, pady=10)
    
    # 定义列
    columns = [
        {
            gui_widgets.TreeviewWithFixedGrid.COL_ID_KEY: 'col1',
            gui_widgets.TreeviewWithFixedGrid.DISPLAY_TEXT_KEY: '列1',
            gui_widgets.TreeviewWithFixedGrid.WIDTH_KEY: 150
        },
        {
            gui_widgets.TreeviewWithFixedGrid.COL_ID_KEY: 'col2',
            gui_widgets.TreeviewWithFixedGrid.DISPLAY_TEXT_KEY: '列2',
            gui_widgets.TreeviewWithFixedGrid.WIDTH_KEY: 150
        },
        {
            gui_widgets.TreeviewWithFixedGrid.COL_ID_KEY: 'col3',
            gui_widgets.TreeviewWithFixedGrid.DISPLAY_TEXT_KEY: '列3',
            gui_widgets.TreeviewWithFixedGrid.WIDTH_KEY: 150
        }
    ]
    
    # 创建TreeviewWithFixedGrid
    try:
        treeview = gui_widgets.TreeviewWithFixedGrid(
            parent=frame,
            columns=columns,
            font_family='Arial',
            font_size=12,
            need_tree=False,
            row_height=25,
            odd_bg='white',
            even_bg='#E8E8E8'
        )
        treeview.pack(fill=BOTH, expand=True)
        
        # 添加测试数据
        test_data = [
            {'col1': f'数据{i}', 'col2': f'内容{i}', 'col3': f'值{i}'}
            for i in range(10)
        ]
        treeview.add_rows(test_data)
        
        print("TreeviewWithFixedGrid创建成功，背景色修复已应用")
        
    except Exception as e:
        print(f"创建TreeviewWithFixedGrid时出错: {e}")
        # 创建一个简单的标签显示错误
        error_label = ttkb.Label(frame, text=f"错误: {e}")
        error_label.pack()
    
    # 居中显示窗口
    root.update_idletasks()
    width = root.winfo_width()
    height = root.winfo_height()
    x = (root.winfo_screenwidth() // 2) - (width // 2)
    y = (root.winfo_screenheight() // 2) - (height // 2)
    root.geometry(f'{width}x{height}+{x}+{y}')
    
    root.mainloop()


if __name__ == "__main__":
    main()
