import tkinter
from tkinter import ttk

from common.models import gui_widgets

# 使用示例
if __name__ == '__main__':
    root = tkinter.Tk()
    root.title('TooltipLabel 示例')
    root.geometry('500x400')

    def build_content():
        tkinter.Label(frame, text='自定义内容', bg='white', font=('Arial', 12, 'bold')).pack(pady=5)
        tkinter.Button(frame, text='确定').pack(side='left', padx=5)
        tkinter.Button(frame, text='取消').pack(side='left')

    frame = ttk.Frame(root, padding=20)
    frame.pack(fill='both', expand=True)

    labels = [
        ('below', '下方'), ('above', '上方'),
        ('left', '左侧'), ('right', '右侧')
    ]
    for i, (pos, txt) in enumerate(labels):
        lbl = gui_widgets.TooltipLabel(frame, build_content, position=pos, text=f'悬浮显示在{txt}')
        lbl.grid(row=i, column=0, padx=10, pady=10)
    lbl = gui_widgets.TooltipLabel(frame, build_content, position='right', text=f'悬浮显示在xxx', offset_x=-50, offset_y=0)
    lbl.grid(row=4, column=0, padx=10, pady=10)
    root.mainloop()
