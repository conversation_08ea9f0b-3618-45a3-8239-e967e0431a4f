"""
测试随机等待功能的示例

演示签到流程中的随机等待机制
"""

import time
from datetime import datetime
from zoneinfo import ZoneInfo
from ..base_check_in import BaseCheckIn


class TestRandomWaitCheckIn(BaseCheckIn):
    """测试随机等待功能的签到实现"""
    
    def __init__(self, username: str, password: str, simulate_already_checked: bool = False, **kwargs):
        """初始化测试类
        
        Args:
            username: 用户名
            password: 密码
            simulate_already_checked: 是否模拟已经签到的情况
            **kwargs: 其他参数
        """
        super().__init__(username, password, **kwargs)
        self.simulate_already_checked = simulate_already_checked
        self.login_attempts = 0
        self.check_in_attempts = 0
    
    def is_login_successful(self) -> bool:
        """模拟登录成功检查"""
        self.logger.debug("checking login status (simulated)")
        # 模拟登录总是成功
        return True
    
    def login_with_credentials(self) -> bool:
        """模拟账密登录"""
        self.login_attempts += 1
        self.logger.info(f"simulating credentials login (attempt {self.login_attempts})")
        # 模拟登录耗时
        time.sleep(1)
        return True
    
    def login_with_cookie(self) -> bool:
        """模拟cookie登录"""
        self.login_attempts += 1
        self.logger.info(f"simulating cookie login (attempt {self.login_attempts})")
        # 模拟登录耗时
        time.sleep(0.5)
        return True
    
    def is_already_checked_in(self) -> bool:
        """模拟签到状态检查"""
        self.logger.debug(f"checking if already checked in (simulate_already_checked: {self.simulate_already_checked})")
        return self.simulate_already_checked
    
    def perform_check_in(self) -> bool:
        """模拟签到操作"""
        self.check_in_attempts += 1
        self.logger.info(f"simulating check-in operation (attempt {self.check_in_attempts})")
        # 模拟签到耗时
        time.sleep(2)
        return True


def test_random_wait_functionality():
    """测试随机等待功能"""
    print("=" * 60)
    print("测试随机等待功能")
    print("=" * 60)
    
    # 测试1: 模拟需要签到的情况（会触发随机等待）
    print("\n📋 测试1: 模拟需要签到的情况")
    print("-" * 40)
    
    start_time = datetime.now()
    
    with TestRandomWaitCheckIn(
        username="test_user",
        password="test_pass",
        simulate_already_checked=False,  # 模拟未签到
        timezone=ZoneInfo("Asia/Shanghai"),
        headless=True
    ) as checker:
        success, message = checker.check_in()
        
    end_time = datetime.now()
    total_time = (end_time - start_time).total_seconds()
    
    print(f"✅ 结果: {success}")
    print(f"📝 消息: {message}")
    print(f"⏱️ 总耗时: {total_time:.2f} 秒")
    print(f"🔍 预期: 应该包含20-30秒的随机等待时间")
    
    # 测试2: 模拟已经签到的情况（不会触发随机等待）
    print("\n📋 测试2: 模拟已经签到的情况")
    print("-" * 40)
    
    start_time = datetime.now()
    
    with TestRandomWaitCheckIn(
        username="test_user2",
        password="test_pass2",
        simulate_already_checked=True,  # 模拟已签到
        timezone=ZoneInfo("Asia/Shanghai"),
        headless=True
    ) as checker:
        success, message = checker.check_in()
        
    end_time = datetime.now()
    total_time = (end_time - start_time).total_seconds()
    
    print(f"✅ 结果: {success}")
    print(f"📝 消息: {message}")
    print(f"⏱️ 总耗时: {total_time:.2f} 秒")
    print(f"🔍 预期: 应该很快完成，不包含随机等待")
    
    print("\n" + "=" * 60)
    print("测试完成")
    print("=" * 60)


if __name__ == "__main__":
    test_random_wait_functionality()
