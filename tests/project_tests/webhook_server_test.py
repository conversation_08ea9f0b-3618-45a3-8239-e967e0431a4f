#!/usr/bin/env python3
"""
简化的配置文件错误测试脚本
专门用于测试 tests/other_conf/error 目录下包含子目录的所有配置文件
"""

from models import server_properties
from src.config import constants
from test_common_utils import setup_test_environment, setup_mock_messagebox, run_test_with_config_test_function,config_manager

# 设置测试环境
setup_test_environment()


def _config_file_test(config_path):
    """测试单个配置文件"""
    # 设置模拟messagebox
    setup_mock_messagebox()
    
    try:
        # 运行 webhook server所需要的检测逻辑
        server_properties.ServerProperties(config_path, constants.DEFAULT_SCHEDULER_CONFIG)
        config_manager.register_config_only_command(file_path=config_path)
        return False, "程序正常启动"
        
    except Exception as e:
        error_msg = f"捕获到异常: {str(e)}"
        return True, error_msg

def main():
    """主函数"""
    # 使用公共函数运行测试，包含子目录
    run_test_with_config_test_function(_config_file_test, include_subdirs=True)

if __name__ == "__main__":
    main()
