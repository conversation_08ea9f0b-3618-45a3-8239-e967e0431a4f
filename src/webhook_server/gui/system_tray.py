"""系统托盘组件。

提供系统托盘功能，包括最小化到托盘、托盘菜单等。
"""
import threading
from tkinter import messagebox
from typing import Optional

import pystray
from PIL import Image
from pystray import MenuItem

from common.utils import logging_utils, gui_utils
from webhook_server.config import gui_constants
from webhook_server.gui import status_monitor, server_control
from webhook_server.models import gui_properties


class SystemTrayComponent:
    """系统托盘组件类。"""

    def __init__(self, props: gui_properties.GUIProperties,status_monitor_co:status_monitor.StatusMonitorComponent,server_control_co:server_control.ServerControlComponent):
        """初始化系统托盘组件。

        Args:
            props: GUI属性
            status_monitor_co: 状态监控组件 --- 在关闭程序时，需要先停止监控
            server_control_co: 服务器控制组件 --- 在关闭程序时，需要先停止服务器进程
        """
        self.props = props
        self.status_monitor_co = status_monitor_co
        self.server_control_co = server_control_co

        self.tray_icon: Optional['pystray.Icon'] = None
        self.tray_thread: Optional[threading.Thread] = None
        self.is_tray_running = False
        # 防重复恢复的标志
        self._restoring_from_tray = False

    def create_tray_icon(self):
        """创建系统托盘图标"""
        try:
            # 加载图标
            icon_image = Image.open(gui_constants.SMALL_ICON_PATH)

            # 创建托盘菜单 - 使用default=True使"显示主界面"成为默认操作
            menu = pystray.Menu(
                MenuItem('显示主界面', self.restore_from_tray, default=True),
                pystray.Menu.SEPARATOR,
                MenuItem('退出程序', self.quit_application)
            )

            # 创建托盘图标
            self.tray_icon = pystray.Icon(
                gui_constants.SOFTWARE_NAME,
                icon_image,
                menu=menu
            )

            logging_utils.logger_print(
                msg="tray icon created successfully with menu-based restore functionality",
                custom_logger=self.props.logger
            )
            return True
        except Exception as e:
            logging_utils.logger_print(
                msg=f"failed to create tray icon: {e}", 
                custom_logger=self.props.logger
            )
            return False

    def show_tray_icon(self) -> bool:
        """显示系统托盘图标"""
        if self.is_tray_running:
            return True

        try:
            # 在单独线程中运行托盘图标
            self.tray_thread = threading.Thread(
                target=self._run_tray_icon,
                daemon=True
            )
            self.tray_thread.start()
            self.is_tray_running = True
            
            logging_utils.logger_print(
                msg="system tray icon shown", 
                custom_logger=self.props.logger
            )
            return True
            
        except Exception as e:
            logging_utils.logger_print(
                msg=f"failed to show tray icon: {e}", 
                custom_logger=self.props.logger
            )
            return False

    def _run_tray_icon(self):
        """在单独线程中运行托盘图标"""
        try:
            if self.tray_icon:
                self.tray_icon.run()
        except Exception as e:
            logging_utils.logger_print(
                msg=f"tray icon thread error: {e}", 
                custom_logger=self.props.logger
            )

    def hide_tray_icon(self):
        """隐藏系统托盘图标"""
        if not self.is_tray_running or not self.tray_icon:
            return

        try:
            self.tray_icon.stop()
            self.is_tray_running = False
            
            logging_utils.logger_print(
                msg="system tray icon hidden", 
                custom_logger=self.props.logger
            )
            
        except Exception as e:
            logging_utils.logger_print(
                msg=f"failed to hide tray icon: {e}", 
                custom_logger=self.props.logger
            )

    def minimize_to_tray(self) -> bool:
        """最小化窗口到系统托盘"""
        try:
            # 创建并显示托盘图标（如果还没有）
            if not self.is_tray_running:
                if not self.create_tray_icon():
                    return False
                if not self.show_tray_icon():
                    return False

            # 隐藏主窗口
            if self.props.root:
                self.props.root.withdraw()
                
            logging_utils.logger_print(
                msg="window minimized to system tray", 
                custom_logger=self.props.logger
            )
            return True
            
        except Exception as e:
            logging_utils.logger_print(
                msg=f"failed to minimize to tray: {e}", 
                custom_logger=self.props.logger
            )
            return False

    def restore_from_tray(self, icon=None, item=None): # noqa
        """从系统托盘恢复窗口:
           注意：pystray 的回调可能会传入额外参数(icon, item)，因此接受可选参数以避免 TypeError。
        """
        # 防止重复调用
        if self._restoring_from_tray:
            logging_utils.logger_print(
                msg="restore already in progress, ignoring duplicate call",
                custom_logger=self.props.logger
            )
            return

        self._restoring_from_tray = True

        try:
            logging_utils.logger_print(
                msg="restoring window from tray",
                custom_logger=self.props.logger
            )

            if not self.props.root:
                logging_utils.logger_print(
                    msg="cannot restore: root window is None",
                    custom_logger=self.props.logger
                )
                return

            # 检查窗口是否已经可见
            current_state = self.props.root.state()
            logging_utils.logger_print(
                msg=f"current window state: {current_state}",
                custom_logger=self.props.logger
            )
            # 恢复窗口显示
            if current_state in ('withdrawn', 'iconic'):
                self.props.root.deiconify()

            # 确保窗口正常显示
            self.props.root.state('normal')
            # # 将窗口置于最前面
            gui_utils.bring_to_front(self.props.root)

            logging_utils.logger_print(
                msg="window restored from tray successfully",
                custom_logger=self.props.logger
            )
        except Exception as e:
            logging_utils.logger_print(
                msg=f"error restoring from tray: {e}",
                custom_logger=self.props.logger
            )
        finally:
            # 重置恢复标志
            self._restoring_from_tray = False

    def quit_application(self, icon=None, item=None): # noqa
        """退出应用程序"""
        # 考虑到配置在加载时出现异常和配置时出现异常的情况,这个时候保存配置就会使得原本在配置文件中的配置丢失,所以这里不保存配置
        # 关闭前检查服务端是否运行
        if self.props.server_process_alive():
            if messagebox.askokcancel("退出", "服务端正在运行，确定要退出吗？",parent=self.props.root):
                self.server_control_co.stop_server()
            else:
                # 取消退出
                return

        # 确保停止所有监控器
        self.status_monitor_co.stop_server_process_monitor()

        # 清理系统托盘
        self.cleanup()

        gui_utils.gui_close(self.props.root)
        self.props.root=None

    def cleanup(self):
        """清理资源"""
        try:
            self.hide_tray_icon()
            if self.tray_thread and self.tray_thread.is_alive():
                self.tray_thread.join(timeout=1.0)
            # 彻底清理引用
            self.tray_thread = None
            self.tray_icon = None
            self.is_tray_running = False
            logging_utils.logger_print(
                msg="system tray component cleaned up", 
                custom_logger=self.props.logger
            )
            
        except Exception as e:
            logging_utils.logger_print(
                msg=f"error during tray cleanup: {e}", 
                custom_logger=self.props.logger
            )
