# 通用型签到模板

## 📋 概述

通用型签到模板提供标准化的签到流程，包括登录验证、cookie管理、签到状态检查等功能。子类只需实现具体的业务逻辑方法即可。

## 🏗️ 类属性说明

### BaseCheckIn 父类属性

```python
class BaseCheckIn(ABC):
    # ========== 核心属性 ==========
    username: str                           # 用户名
    password: str                           # 密码
    session: requests.Session               # HTTP会话对象，包含超时配置
    
    # ========== 文件路径属性 ==========
    cookie_file_path: str                   # cookie文件保存路径
    log_file_path: str                      # 日志文件路径
    
    # ========== 日志配置属性 ==========
    logger: logging.Logger                  # 日志记录器（同时输出到控制台和文件）
    log_level: int                          # 日志级别（默认INFO）
    
    # ========== 网络配置属性 ==========
    request_timeout: int                    # 请求超时时间（秒，默认30秒）
    
    # ========== 自动化配置属性 ==========
    headless: bool                          # 自动化操作是否可视化（True为无头模式，False为可视化）
    timezone: ZoneInfo                      # 时区设置（默认Asia/Shanghai）
    
    # ========== 状态属性 ==========
    is_logged_in: bool                      # 当前登录状态
    last_check_in_time: Optional[datetime]  # 上次签到时间（带时区）
    login_method: Optional[str]             # 当前登录方式（cookie/credentials）
```

## 🎯 抽象方法

子类需要实现的5个抽象方法：

1. **`is_login_successful()`** - 判断登录是否成功
2. **`login_with_credentials()`** - 使用账户和密码登录
3. **`login_with_cookie()`** - 尝试使用cookie登录
4. **`is_already_checked_in()`** - 判断是否已经签到
5. **`perform_check_in()`** - 执行签到操作

## 🔧 使用方式

### 基本使用

```python
from src.check_in.examples.sample_check_in import SampleCheckIn

# 基本使用
with SampleCheckIn(
    username="your_username",
    password="your_password",
    base_url="https://example.com"
) as checker:
    success, message = checker.check_in()
    print(f"结果: {success}, 消息: {message}")
```

### 自定义配置

```python
from zoneinfo import ZoneInfo

# 自定义配置
with SampleCheckIn(
    username="your_username",
    password="your_password",
    base_url="https://example.com",
    headless=False,  # 可视化模式
    timezone=ZoneInfo("Asia/Shanghai"),  # 指定时区
    log_file_path="custom_check_in.log",  # 自定义日志文件
    request_timeout=60  # 60秒超时
) as checker:
    success, message = checker.check_in()
    print(f"结果: {success}, 消息: {message}")
```

## ✨ 主要特性

### 📝 双重日志输出
- **控制台输出**：实时查看运行状态
- **文件输出**：持久化保存日志记录
- **时区支持**：日志时间戳使用指定时区
- **自动创建**：自动创建日志目录

### 🔄 完整的签到流程
1. 尝试使用cookie登录
2. 如果cookie登录失败，使用账密登录
3. 如果账密登录也失败，停止操作
4. 登录成功后检查是否已签到
5. 如果已签到，直接返回成功
6. 如果未签到，随机等待20-30秒（模拟人工操作）
7. 执行签到操作

### 🛡️ 异常处理机制
- 每个步骤都有完整的异常捕获
- 详细的错误日志记录
- 优雅的资源清理

### 🎮 自动化控制
- **headless=True**：无头模式，适合服务器环境
- **headless=False**：可视化模式，适合调试和开发

### ⏱️ 智能等待机制
- **随机等待**：登录成功但未签到时，随机等待20-30秒
- **模拟人工**：避免机器行为被检测，提高成功率
- **详细日志**：记录等待时间和签到结果

## 📁 目录结构

```
src/check_in/
├── __init__.py                    # 模块初始化
├── base_check_in.py              # 通用签到父类
├── README.md                     # 使用文档
└── examples/
    ├── __init__.py
    └── sample_check_in.py        # 完整的示例实现
```

## 🔍 日志格式

日志格式包含时区信息：
```
2024-01-15 14:30:25 CST - check_in_SampleCheckIn_username - INFO - starting check-in process for user: username
```

## 📋 更新记录

- ✅ 移除了多余的 `max_retry_count` 属性
- ✅ 新增 `headless` 属性控制自动化操作可视化
- ✅ 新增时区字段，日志记录时应用指定时区
- ✅ 完善了日志格式化器，支持时区显示
- ✅ 更新了示例代码，展示新属性的使用方法
- ✅ 新增随机等待机制，在签到前等待20-30秒模拟人工操作
