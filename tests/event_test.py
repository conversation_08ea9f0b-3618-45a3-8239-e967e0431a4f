# 测试wait是否可以及时停止

import threading
import time

def heartbeat_thread(stop_event):
    """子线程心跳任务"""
    counter = 0
    while True:  # 检查事件状态
        counter += 1
        print(f"Heartbeat #{counter} - Thread alive")
        if stop_event.wait(timeout=4):
            break  # 事件已被设置，退出循环

    print("Heartbeat thread received stop signal. Exiting...")

if __name__ == "__main__":
    # 创建停止事件对象
    stop_heartbeat_event = threading.Event()

    # 创建并启动子线程
    heartbeat = threading.Thread(target=heartbeat_thread, args=(stop_heartbeat_event,))
    heartbeat.start()

    print("Main thread sleeping for 10 seconds...")
    time.sleep(10)  # 主线程等待10秒

    # 设置停止事件
    print("Main thread sending stop signal...")
    stop_heartbeat_event.set()

    # 等待子线程结束
    heartbeat.join()
    print("Main thread: Heartbeat thread has terminated")
