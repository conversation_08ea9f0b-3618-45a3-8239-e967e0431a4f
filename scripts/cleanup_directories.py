#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
目录清理脚本
删除项目根目录下除指定保留目录外的所有非隐藏目录
保留目录: scripts, doc, resources, src, tests, venv
"""

import os
import shutil
import sys
from pathlib import Path


def cleanup_directories():
    """清理项目目录，删除除保留目录外的所有非隐藏目录"""
    
    # 保留的目录列表
    keep_dirs = {'scripts', 'doc', 'resources', 'src', 'tests', 'venv'}
    
    # 获取项目根目录
    project_root = Path(__file__).parent.parent
    print(f"项目根目录: {project_root}")
    
    # 获取所有非隐藏目录
    all_dirs = [d for d in project_root.iterdir() 
                if d.is_dir() and not d.name.startswith('.')]
    
    # 找出需要删除的目录
    dirs_to_delete = [d for d in all_dirs if d.name not in keep_dirs]
    
    if not dirs_to_delete:
        print("没有需要删除的目录")
        return
    
    print(f"保留目录: {', '.join(sorted(keep_dirs))}")
    print(f"将要删除的目录: {', '.join([d.name for d in dirs_to_delete])}")
    
    # 确认删除
    response = input("\n确认删除以上目录吗? (y/N): ").strip().lower()
    if response not in ['y', 'yes']:
        print("操作已取消")
        return
    
    # 执行删除
    deleted_count = 0
    for dir_path in dirs_to_delete:
        try:
            shutil.rmtree(dir_path)
            print(f"✓ 已删除: {dir_path.name}")
            deleted_count += 1
        except Exception as e:
            print(f"✗ 删除失败 {dir_path.name}: {e}")
    
    print(f"\n清理完成，共删除 {deleted_count} 个目录")


if __name__ == "__main__":
    try:
        cleanup_directories()
    except KeyboardInterrupt:
        print("\n操作被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"发生错误: {e}")
        sys.exit(1)