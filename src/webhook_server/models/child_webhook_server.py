import argparse
import asyncio
import sys
from pathlib import Path



src_path = str(Path(sys._MEIPASS) if getattr(sys, 'frozen', False) else Path(__file__).parent.parent.parent) # noqa

# 确保源代码路径在sys.path中,且在第一位
if src_path in sys.path:
    sys.path.remove(src_path)
sys.path.insert(0,src_path)

from webhook_server.models import webhook_server


if __name__ == "__main__":
    """是直接给gui打开webhook server子进程的入口"""
    parser = argparse.ArgumentParser()
    parser.add_argument("--config", required=True, help="path to server config file")
    asyncio.run(webhook_server.run_server_with_path(config_path=parser.parse_args().config, is_child_process=True))
