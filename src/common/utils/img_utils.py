from PIL  import Image


def resize_image_pillow(resized_img: Image, new_width, new_height):
    """
    使用Pillow库调整图像尺寸

    Args:
        :param new_width: 目标宽度
        :param new_height: 目标高度
        :param resized_img: 需要调整尺寸的图像对象
    """
    # 调整尺寸
    new_width = int(round(new_width))
    new_height = int(round(new_height))
    return resized_img.resize((new_width, new_height), Image.Resampling.LANCZOS)
