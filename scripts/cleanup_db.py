import os
import sqlite3

from config import constants


def clean_sqlite_db():
    """清空当前数据库中的所有表和表数据"""
    db_path = constants.CROSS_PROCESS_DATA_BASE_PATH
    print(f"清理数据库: {db_path}")

    if not os.path.exists(db_path):
        print(f"数据库文件不存在，无需清理: {db_path}")
        return

    try:
        # 连接数据库
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()

        # 获取所有表名
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
        tables = cursor.fetchall()

        if not tables:
            print("数据库中没有表，无需清理")
            return

        # 关闭外键约束
        cursor.execute("PRAGMA foreign_keys = OFF;")

        # 开始事务
        cursor.execute("BEGIN TRANSACTION;")

        # 删除每个表中的所有数据
        for table in tables:
            table_name = table[0]
            print(f"  - 删除表 '{table_name}' 中的所有数据")
            cursor.execute(f"DELETE FROM {table_name};")

        # 提交事务
        conn.commit()

        print(f"成功清理数据库中的所有表数据")

    except sqlite3.Error as e:
        print(f"清理数据库时出错: {e}")
    finally:
        if 'conn' in locals():
            conn.close()  # noqa


if __name__ == '__main__':
    clean_sqlite_db()
