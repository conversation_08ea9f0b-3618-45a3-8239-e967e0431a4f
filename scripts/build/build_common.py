#!/usr/bin/env python3
"""
PyInstaller打包公共模块
包含Windows和Linux平台的公共代码和变量
"""

import shutil
import subprocess
import sys
from pathlib import Path

sys.path.insert(0, str(Path(__file__).parent.parent.parent / "src"))
from webhook_server.config import gui_constants

# 公共变量
SOFTWARE_NAME = gui_constants.SPEC_SOFTWARE_NAME
SPEC_FILE_NAME = "webhook_server_gui.spec"
MAIN_SCRIPT_PATH = "src/webhook_server/config_selection_gui.py"
RESOURCE_FILE_PATH = "resources/22.png"
PROJECT_ROOT = Path(__file__).parent.parent.parent.absolute()
DIST_DIR=PROJECT_ROOT/"dist"

def print_header(platform_name):
    """打印构建头部信息"""
    print("=" * 50)
    print(f"{platform_name}平台 PyInstaller 打包工具")
    # cleanup_db.clean_sqlite_db()
    print("=" * 50)


def check_key_files():
    """
    检查关键文件是否存在
    :return: 检查是否通过
    """
    # 检查spec文件
    spec_file = PROJECT_ROOT / SPEC_FILE_NAME
    if not spec_file.exists():
        print(f"错误: spec文件不存在: {spec_file}")
        return False

    # 检查主脚本
    main_script = PROJECT_ROOT / MAIN_SCRIPT_PATH
    if not main_script.exists():
        print(f"错误: 主脚本不存在: {main_script}")
        return False

    # 检查资源文件（警告级别）
    resource_file = PROJECT_ROOT / RESOURCE_FILE_PATH
    if not resource_file.exists():
        print(f"警告: 资源文件不存在: {resource_file}")

    print("✓ 关键文件检查通过")
    return True


def check_and_install_pyinstaller():
    """
    检查PyInstaller是否安装，如果没有则安装
    :return: 检查是否成功
    """
    try:
        import PyInstaller
        print(f"✓ PyInstaller版本: {PyInstaller.__version__}")
        return True
    except ImportError:
        print("安装PyInstaller...")
        result = subprocess.run([sys.executable, "-m", "pip", "install", "pyinstaller"],
                              capture_output=True, text=True)
        if result.returncode != 0:
            print(f"错误: PyInstaller安装失败: {result.stderr}")
            return False
        print("✓ PyInstaller安装成功")
        return True

def cleanup_build_dirs(additional_cleanup=None):
    """
    清理构建目录
    :param additional_cleanup: 额外的清理函数
    """
    print("清理之前的构建...")
    try:
        # 执行额外的清理操作
        if additional_cleanup:
            additional_cleanup()

        # 清理构建目录
        for dir_name in ["build", "dist", "release"]:
            dir_path = PROJECT_ROOT / dir_name
            if dir_path.exists():
                shutil.rmtree(dir_path)
        print("✓ 清理完成")
    except Exception as e:
        print(f"警告: 清理过程中出现问题: {e}")

def run_pyinstaller():
    """
    执行PyInstaller打包
    :return: 是否成功
    """
    print("开始打包...")
    spec_file = PROJECT_ROOT / SPEC_FILE_NAME
    cmd = [
        sys.executable, "-m", "PyInstaller",
        "--clean",
        "--noconfirm",
        str(spec_file)
    ]

    print(f"执行命令: {' '.join(cmd)}")
    result = subprocess.run(cmd, cwd=PROJECT_ROOT)

    if result.returncode != 0:
        print("打包失败！")
        return False

    return True

def create_release_dir( platform):
    """
    创建发布目录
    :param platform: 平台名称 (windows/linux)
    :return: 发布目录路径
    """
    release_dir = PROJECT_ROOT / "release" / platform
    release_dir.mkdir(parents=True, exist_ok=True)
    return release_dir

def print_success_message(platform_name):
    """打印成功消息"""
    print("=" * 50)
    print(f"{platform_name}打包完成！")

def print_failure_message(platform_name):
    """打印失败消息"""
    print("=" * 50)
    print(f"{platform_name}打包失败！")

def copy_file_with_info(src, dst, description="文件"):
    """
    复制文件并显示信息
    :param src: 源文件路径
    :param dst: 目标文件路径
    :param description: 文件描述
    """
    shutil.copy2(src, dst)
    print(f"✓ {description}已复制到: {dst}")

def copy_tree_with_info(src, dst, description="目录"):
    """
    复制目录并显示信息
    :param src: 源目录路径
    :param dst: 目标目录路径
    :param description: 目录描述
    """
    if dst.exists():
        shutil.rmtree(dst)
    shutil.copytree(src, dst)
    print(f"✓ {description}已复制到: {dst}")

def get_file_size_mb(file_path):
    """
    获取文件大小（MB）
    :param file_path: 文件路径
    :return: 文件大小（MB）
    """
    return file_path.stat().st_size / 1024 / 1024

def main_build_flow(platform_name,  additional_cleanup=None, post_build_handler=None):
    """
    主要构建流程
    :param platform_name: 平台显示名称
    :param additional_cleanup: 额外清理函数
    :param post_build_handler: 构建后处理函数
    :return: 是否成功
    """
    print_header(platform_name)

    # 获取项目根目录
    print(f"项目目录: {PROJECT_ROOT}")

    # 检查关键文件
    if not check_key_files():
        return False

    # 检查PyInstaller
    if not check_and_install_pyinstaller():
        return False

    # 清理之前的构建
    cleanup_build_dirs( additional_cleanup)

    # 执行打包
    if not run_pyinstaller():
        return False

    # 执行构建后处理
    if post_build_handler:
        if not post_build_handler():
            return False

    return True
