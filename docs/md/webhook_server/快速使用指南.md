# 🚀 NexusRecv 快速使用指南

[![Version](https://img.shields.io/badge/version-v1.0.2-blue.svg)](../README.md)
[![Platform](https://img.shields.io/badge/platform-Windows%20%7C%20Linux%20%7C%20macOS-lightgrey.svg)](../README.md)
[![License](https://img.shields.io/badge/license-Apache%202.0-green.svg)](../README.md)

## 📋 概述

本指南将帮助您快速上手NexusRecv，从安装配置到设备接入，让您在最短时间内开始使用这个强大的设备数据接收平台。

### 🎯 适用对象
- **新用户**: 第一次使用NexusRecv的用户
- **系统管理员**: 需要快速部署的管理员
- **开发者**: 需要集成API的开发人员

## 🚀 快速开始

### 📦 1. 获取程序

#### 方式一：下载可执行文件（推荐）
> 📝 注意：当前版本需要通过源码构建获取可执行文件

```bash
# 1. 克隆项目
git clone https://github.com/your-repo/nexusrecv.git
cd nexusrecv

# 2. 安装构建依赖
pip install -r scripts/build/requirements_build.txt

# 3. 构建可执行文件
python scripts/build/build_by_platform.py
```

**构建输出**:
- **🪟 Windows**: `release/windows/NexusRecv.exe`
- **🐧 Linux**: `release/linux/NexusRecv/` 目录 + deb安装包

#### 方式二：源码运行（开发环境）
```bash
# 1. 安装Python依赖
pip install -r requirements.txt

# 2. 直接运行
python src/webhook_server/config_selection_gui.py
```

### ⚡ 2. 运行程序

#### 🪟 Windows用户
1. **双击运行**: 双击 `NexusRecv.exe` 启动程序
2. **配置选择**: 首次运行会显示配置选择界面

#### 🐧 Linux用户
```bash
# 方式1: 使用deb包安装
sudo dpkg -i release/nexusrecv-webhook-server_1.0.2_amd64.deb
nexusrecv-webhook-server

# 方式2: 直接运行
cd release/linux/NexusRecv
./NexusRecv
```

### ⚙️ 3. 配置服务器

#### 🎯 首次配置流程

**步骤1: 选择配置模式**
- 🆕 **新用户**: 选择"使用示例配置"快速开始
- 📁 **有经验用户**: 选择"创建新配置"自定义设置
- 🔄 **已有配置**: 选择现有配置文件

**步骤2: 基础配置设置**

在配置界面中需要设置以下关键参数：

#### 🔐 安全配置
```ini
[server]
# 🔑 API密钥（重要！请使用强密码）
api_key = your_secure_api_key_here

# 🛡️ IP白名单（控制访问权限）
whitelist = ***********/24,127.0.0.1
```

#### 🌐 网络配置
```ini
# 🔌 服务器监听地址和端口
host = 0.0.0.0
port = 8000

# ⏰ 运行时间段（24小时制）
run_time = 07:00-23:00
```

#### 📱 设备配置
```ini
[client_info]
# 📱 设备标识和描述（10-30字符，仅字母数字）
device001 = 我的第一个设备
sensor001 = 温湿度传感器
gateway001 = 物联网网关
```

#### ✅ 配置验证

程序会自动验证配置：
- ✅ **格式检查**: INI格式是否正确
- ✅ **必填项**: 所有必填配置是否完整
- ✅ **数据类型**: 配置值类型是否正确
- ✅ **取值范围**: 配置值是否在有效范围

**步骤3: 保存配置**
- 配置验证通过后，点击"保存"按钮
- 配置文件会自动保存到用户目录
- 下次启动时可以选择此配置

### 🚀 4. 启动服务

#### 🎮 启动服务器
1. **启动服务**: 完成配置后，在主界面点击"启动服务器"按钮
2. **状态确认**: 服务器启动成功后，界面显示"🟢 服务器运行中"
3. **服务信息**: 可以看到服务器的IP地址、端口和运行状态

#### 📊 服务器监控
- **📈 实时数据**: 接收到的数据实时显示在主界面表格中
- **💻 系统信息**: 查看服务器运行时长、CPU和内存使用情况
- **🎛️ 控制操作**: 支持一键启动/停止服务器
- **📝 日志查看**: 实时查看服务器运行日志

## 📱 设备接入使用

### 🔌 1. 发送数据

#### HTTP API调用

使用任何支持HTTP的设备或程序发送数据：

```bash
# 📝 基本格式
curl -X POST http://服务器IP:端口/webhook/save \
  -H "Content-Type: application/json" \
  -H "X-Client-Key: 设备标识" \
  -d '{"content":"数据内容"}'

# 🌡️ 温度传感器示例
curl -X POST http://*************:8000/webhook/save \
  -H "Content-Type: application/json" \
  -H "X-Client-Key: device001" \
  -d '{"content":"温度:25.6°C,湿度:60%"}'

# 📊 压力传感器示例
curl -X POST http://*************:8000/webhook/save \
  -H "Content-Type: application/json" \
  -H "X-Client-Key: sensor001" \
  -d '{"content":"压力:1013.25hPa"}'
```

#### 📋 发送要求

| 项目 | 要求 | 说明 |
|------|------|------|
| **🔑 设备标识** | 10-30字符 | 必须在配置中预先添加 |
| **📝 数据内容** | 1-100字符 | JSON格式，content字段 |
| **🌐 网络协议** | HTTP/HTTPS | 支持keep-alive连接 |
| **📡 请求头** | X-Client-Key | 必须包含设备标识 |

#### ⚠️ 注意事项
- ✅ 设备标识必须在`[client_info]`中预先配置
- ✅ 数据内容长度严格限制在1-100字符
- ✅ 服务器IP和端口以实际配置为准
- ✅ 确保设备与服务器在同一局域网内

### 2. 查看数据

#### GUI界面查看
- 服务器启动后，接收到的数据会实时显示在主界面的表格中
- 双击表格行可复制完整数据内容
- 可以按设备标识筛选数据
- 支持数据搜索功能

### 3. 系统托盘

- 点击窗口关闭按钮会最小化到系统托盘
- 右键托盘图标可以：
  - 显示/隐藏主窗口
  - 启动/停止服务器
  - 退出程序

## 常用功能

### 1. 多配置管理

- 支持创建多个配置文件
- 每个配置文件可以独立运行
- 配置文件自动保存在用户目录的`.webhook_server`文件夹

### 2. 数据管理

- 数据自动保存到SQLite数据库
- 支持设置数据过期时间（天数）
- 支持设置数据存储上限（条数）
- 过期和超量数据会自动清理

### 3. 日志查看

- 日志文件保存在`logs`目录
- 支持不同级别的日志记录
- 日志文件按日期自动分割

## 网络配置

### 1. 防火墙设置

#### Windows
- 首次运行时Windows会询问是否允许网络访问
- 选择"允许访问"即可

#### Linux
```bash
# 开放端口（以8000为例）
sudo ufw allow 8000/tcp
```

### 2. 局域网访问

- 确保服务器配置中`host = 0.0.0.0`
- 其他设备使用服务器IP地址访问
- 例如：`http://*************:8000/webhook/save`

### 3. IP白名单

```ini
# 允许所有IP
whitelist = *

# 允许特定IP
whitelist = *************,*************

# 允许网段
whitelist = ***********/24

# 混合配置
whitelist = ***********/24,********,127.0.0.1
```

## 故障排除

### 1. 服务无法启动

**检查端口占用**
```bash
# Windows
netstat -an | findstr :8000

# Linux
netstat -tuln | grep :8000
```

**解决方法**
- 更改配置文件中的端口号
- 或者停止占用端口的其他程序

### 2. 设备无法发送数据

**检查设备标识**
- 确保设备标识在配置文件的`[client_info]`中存在
- 设备标识区分大小写
- 设备标识只能包含字母和数字

**检查网络连接**
- 确保设备和服务器在同一网络
- 检查防火墙设置
- 测试网络连通性：`ping 服务器IP`

### 3. GUI界面问题

**界面无响应**
- 检查配置文件格式是否正确
- 查看日志文件中的错误信息
- 重启程序

**系统托盘不显示**
- 检查系统托盘设置
- 确保系统支持托盘功能

## 高级配置

### 1. 定时运行

```ini
[server]
# 每天7点到23点运行
run_time = 07:00-23:00

# 24小时运行
run_time = 00:00-00:00
```

### 2. 时区设置

```ini
[server]
# 设置时区
time_zone = Asia/Shanghai
```

### 3. 数据清理策略

```ini
[server]
# 数据保留7天
expire_data_days = 7

# 最多保存10万条数据
data_limit_num = 100000

# 永不过期（设置为0或负数）
expire_data_days = 0

# 不限制数量（设置为0或负数）
data_limit_num = 0
```

## 技术支持

### 程序文件说明

#### Windows版本
- `NexusRecv.exe` - 主程序可执行文件
- 程序运行时会自动在用户目录创建 `.webhook_server` 文件夹
- 配置文件和数据库文件都保存在此文件夹中

#### Linux版本
- **deb包安装**: 程序安装到系统目录，命令为`nexusrecv-webhook-server`
- **直接运行**: 使用构建生成的`NexusRecv`可执行文件
- 配置和数据文件同样保存在用户目录的 `.webhook_server` 文件夹

### 联系方式
- 邮箱：<EMAIL>

### 常见问题
1. **Q: 可以同时运行多个实例吗？**
   A: 可以，但需要使用不同的配置文件和端口。

2. **Q: 数据存储在哪里？**
   A: 数据存储在用户目录的`.webhook_server/config_data.db`文件中。

3. **Q: 如何备份数据？**
   A: 复制`.webhook_server`整个文件夹即可。

4. **Q: 支持HTTPS吗？**
   A: 目前不直接支持，建议使用nginx等反向代理实现HTTPS。

5. **Q: Linux下提示权限不足怎么办？**
   A: 给程序文件添加执行权限：`chmod +x NexusRecv` 或 `chmod +x start.sh`

6. **Q: Windows下被杀毒软件拦截怎么办？**
   A: 将程序添加到杀毒软件的白名单中，或临时关闭实时保护。

---

**版本**: v1.0.2  
**更新日期**: 2025-09-03  
**适用软件**: NexusRecv 多类别设备数据接收存储服务端
