"""滑块验证码识别器模块。

使用Playwright和ddddocr API实现滑块拼图验证码的自动识别和操作。
"""

import asyncio
import logging
from typing import Optional, Tuple

from playwright.async_api import async_playwright, <PERSON>, Browser, BrowserContext

from common.utils import logging_utils
from .ddddocr_client import DDDDOCRClient

logger = logging.getLogger(__name__)


class SliderCaptchaRecognizer:
    """滑块验证码识别器。
    
    使用Playwright控制浏览器，结合ddddocr API实现滑块验证码的自动识别和操作。
    """
    
    def __init__(self,
                 ddddocr_base_url: str = "http://10.168.1.201:7777",
                 headless: bool = True,
                 browser_type: str = "chromium"):
        """初始化滑块验证码识别器。

        Args:
            ddddocr_base_url: ddddocr服务的基础URL
            headless: 是否使用无头模式运行浏览器
            browser_type: 浏览器类型 ("chromium", "firefox", "webkit")
        """
        self.ddddocr_client = DDDDOCRClient(ddddocr_base_url)
        self.headless = headless
        self.browser_type = browser_type
        
        self.playwright = None
        self.browser: Optional[Browser] = None
        self.context: Optional[BrowserContext] = None
        self.page: Optional[Page] = None
        
        logging_utils.logger_print(
            msg=f"initialized slider captcha recognizer with browser_type: {browser_type}, headless: {headless}",
            custom_logger=logger
        )
    
    async def __aenter__(self):
        """异步上下文管理器入口。"""
        await self.start()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口。"""
        await self.close()
    
    async def start(self):
        """启动浏览器和页面。"""
        logging_utils.logger_print(
            msg="starting playwright browser",
            custom_logger=logger
        )
        
        self.playwright = await async_playwright().start()
        
        # 选择浏览器类型
        if self.browser_type == "firefox":
            browser_launcher = self.playwright.firefox
        elif self.browser_type == "webkit":
            browser_launcher = self.playwright.webkit
        else:
            browser_launcher = self.playwright.chromium
        
        # 启动浏览器
        self.browser = await browser_launcher.launch(
            headless=self.headless,
            args=[
                '--no-sandbox',
                '--disable-blink-features=AutomationControlled',
                '--disable-web-security',
                '--disable-features=VizDisplayCompositor'
            ]
        )
        
        # 创建浏览器上下文
        self.context = await self.browser.new_context(
            viewport={'width': 1920, 'height': 1080},
            user_agent='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
        )
        
        # 创建页面
        self.page = await self.context.new_page()
        
        logging_utils.logger_print(
            msg="playwright browser started successfully",
            custom_logger=logger
        )
    
    async def close(self):
        """关闭浏览器和相关资源。"""
        logging_utils.logger_print(
            msg="closing playwright browser",
            custom_logger=logger
        )
        
        if self.page:
            await self.page.close()
            self.page = None
        
        if self.context:
            await self.context.close()
            self.context = None
        
        if self.browser:
            await self.browser.close()
            self.browser = None
        
        if self.playwright:
            await self.playwright.stop()
            self.playwright = None
        
        if self.ddddocr_client:
            self.ddddocr_client.close()
        
        logging_utils.logger_print(
            msg="playwright browser closed",
            custom_logger=logger
        )
    
    async def navigate_to_page(self, url: str, wait_timeout: int = 30000):
        """在指定页面打开URL所对应的网页。
        
        Args:
            url: 目标页面URL
            wait_timeout: 等待超时时间（毫秒）
        """
        if not self.page:
            raise RuntimeError("browser not started, call start() first")
        
        logging_utils.logger_print(
            msg=f"navigating to page: {url}",
            custom_logger=logger
        )
        
        await self.page.goto(url, wait_until='domcontentloaded', timeout=wait_timeout)
        
        # 等待页面稳定
        await self.page.wait_for_timeout(2000)
    
    async def capture_element_screenshot(self, selector: str) -> bytes:
        """截取指定标签页中指定元素的截图。
        
        Args:
            selector: 元素选择器
            
        Returns:
            截图的二进制数据
        """
        if not self.page:
            raise RuntimeError("browser not started, call start() first")
        
        element = await self.page.wait_for_selector(selector, timeout=10000)
        if not element:
            raise RuntimeError(f"element not found: {selector}")
        
        screenshot_data = await element.screenshot()
        
        logging_utils.logger_print(
            msg=f"captured screenshot for element: {selector}",
            custom_logger=logger
        )
        
        return screenshot_data
    
    async def get_slider_images(self, 
                               background_selector: str,
                               slider_selector: Optional[str] = None) -> Tuple[bytes, Optional[bytes]]:
        """获取滑块验证码的背景图和滑块图。
        该方法可能不通用，不同的网站获取的方式存在区别,可能需要特定实现
        Args:
            background_selector: 背景图元素选择器
            slider_selector: 滑块图元素选择器（可选）
            
        Returns:
            (背景图数据, 滑块图数据)，滑块图数据可能为None
        """
        logging_utils.logger_print(
            msg="capturing slider captcha images",
            custom_logger=logger
        )
        
        # 截取背景图
        background_data = await self.capture_element_screenshot(background_selector)
        
        # 截取滑块图（如果提供了选择器）
        slider_data = None
        if slider_selector:
            try:
                slider_data = await self.capture_element_screenshot(slider_selector)
            except Exception as e:
                logging_utils.logger_print(
                    msg=f"failed to capture slider image: {e}",
                    custom_logger=logger
                )
        
        return background_data, slider_data
    
    async def solve_slider_captcha(self,
                                  background_selector: str,
                                  slider_button_selector: str,
                                  slider_selector: Optional[str] = None,
                                  use_slide_comparison: bool = True) -> bool:
        """解决滑块验证码。
        
        Args:
            background_selector: 背景图元素选择器
            slider_button_selector: 滑块按钮选择器
            slider_selector: 滑块图元素选择器（可选）
            use_slide_comparison: 是否使用slideComparison API（否则使用capcode API）
            
        Returns:
            是否成功解决验证码
        """
        if not self.page:
            raise RuntimeError("browser not started, call start() first")
        
        logging_utils.logger_print(
            msg="starting to solve slider captcha",
            custom_logger=logger
        )
        
        try:
            # 等待验证码元素加载
            await self.page.wait_for_selector(background_selector, timeout=10000)
            await self.page.wait_for_selector(slider_button_selector, timeout=10000)
            
            # 获取滑块验证码图片
            background_data, slider_data = await self.get_slider_images(
                background_selector, slider_selector
            )
            
            # 使用ddddocr API识别滑块位置
            if use_slide_comparison and slider_data:
                # 使用slideComparison API
                target_x = self.ddddocr_client.slide_comparison(
                    slider_data, background_data
                )
                target_y = 0  # slideComparison只返回X坐标
            elif slider_data:
                # 使用capcode API
                target_x = self.ddddocr_client.capcode(
                    slider_data, background_data
                )
                target_y = 0  # capcode只返回X坐标
            else:
                # 如果没有滑块图，尝试使用classification API
                result = self.ddddocr_client.classification(background_data)
                logging_utils.logger_print(
                    msg=f"classification result: {result}",
                    custom_logger=logger
                )
                # 这里需要根据具体的返回格式解析位置信息
                # 暂时返回失败
                return False

            logging_utils.logger_print(
                msg=f"detected slider target position: x={target_x}",
                custom_logger=logger
            )
            
            # 执行滑块拖拽操作
            success = await self._drag_slider(slider_button_selector, target_x, 0)
            
            if success:
                logging_utils.logger_print(
                    msg="slider captcha solved successfully",
                    custom_logger=logger
                )
            else:
                logging_utils.logger_print(
                    msg="failed to solve slider captcha",
                    custom_logger=logger
                )
            
            return success
            
        except Exception as e:
            logging_utils.logger_print(
                msg="error solving slider captcha",
                custom_logger=logger,
                use_exception=True,
                exception=e
            )
            return False
    
    async def _drag_slider(self, slider_button_selector: str, target_x: int, target_y: int = 0) -> bool:
        """拖拽滑块到目标位置。

        Args:
            slider_button_selector: 滑块按钮选择器
            target_x: 目标X坐标偏移量（相对于起始位置）
            target_y: 目标Y坐标偏移量（通常为0，因为滑块只需要水平移动）

        Returns:
            是否拖拽成功
        """
        try:
            # 获取滑块按钮元素
            slider_button = await self.page.wait_for_selector(slider_button_selector, timeout=5000)
            if not slider_button:
                logging_utils.logger_print(
                    msg=f"slider button not found: {slider_button_selector}",
                    custom_logger=logger
                )
                return False

            # 获取滑块按钮的位置和大小
            button_box = await slider_button.bounding_box()
            if not button_box:
                logging_utils.logger_print(
                    msg="failed to get slider button bounding box",
                    custom_logger=logger
                )
                return False

            # 计算起始位置（滑块按钮中心）
            start_x = button_box['x'] + button_box['width'] / 2
            start_y = button_box['y'] + button_box['height'] / 2

            # 计算目标位置（通常只需要水平移动）
            end_x = start_x + target_x
            end_y = start_y + target_y

            logging_utils.logger_print(
                msg=f"dragging slider from ({start_x:.1f}, {start_y:.1f}) to ({end_x:.1f}, {end_y:.1f}), offset: {target_x}px",
                custom_logger=logger
            )

            # 执行拖拽操作
            await self.page.mouse.move(start_x, start_y)
            await asyncio.sleep(0.1)  # 短暂停顿

            await self.page.mouse.down()
            await asyncio.sleep(0.1)  # 按下后停顿

            # 模拟人类拖拽行为，分多步移动
            steps = max(10, abs(target_x) // 10)  # 根据距离调整步数
            for i in range(1, steps + 1):
                progress = i / steps
                # 使用缓动函数使移动更自然
                eased_progress = progress * progress * (3.0 - 2.0 * progress)  # smoothstep

                current_x = start_x + (end_x - start_x) * eased_progress
                current_y = start_y + (end_y - start_y) * eased_progress

                await self.page.mouse.move(current_x, current_y)
                # 随机化延迟，模拟人类行为
                delay = 0.02 + (i % 3) * 0.01
                await asyncio.sleep(delay)

            # 确保到达最终位置
            await self.page.mouse.move(end_x, end_y)
            await asyncio.sleep(0.1)

            await self.page.mouse.up()

            # 等待验证结果
            await self.page.wait_for_timeout(1500)

            logging_utils.logger_print(
                msg="slider drag completed",
                custom_logger=logger
            )

            return True

        except Exception as e:
            logging_utils.logger_print(
                msg="error dragging slider",
                custom_logger=logger,
                use_exception=True,
                exception=e
            )
            return False
    
    async def wait_for_captcha_result(self, 
                                     success_selector: Optional[str] = None,
                                     failure_selector: Optional[str] = None,
                                     timeout: int = 10000) -> Optional[bool]:
        """等待验证码验证结果。
        
        Args:
            success_selector: 成功标识元素选择器
            failure_selector: 失败标识元素选择器
            timeout: 等待超时时间（毫秒）
            
        Returns:
            验证结果：True表示成功，False表示失败，None表示超时
        """
        if not self.page:
            return None
        
        try:
            if success_selector and failure_selector:
                # 等待成功或失败元素出现
                result = await self.page.wait_for_selector(
                    f"{success_selector}, {failure_selector}",
                    timeout=timeout
                )
                
                if result:
                    # 检查是哪个元素
                    if await result.evaluate(f"el => el.matches('{success_selector}')"):
                        return True
                    else:
                        return False
            elif success_selector:
                # 只等待成功元素
                result = await self.page.wait_for_selector(success_selector, timeout=timeout)
                return result is not None
            elif failure_selector:
                # 只等待失败元素
                result = await self.page.wait_for_selector(failure_selector, timeout=timeout)
                return result is None  # 如果失败元素出现，返回False
            
        except Exception as e:
            logging_utils.logger_print(
                msg="timeout waiting for captcha result",
                custom_logger=logger
            )
        
        return None
