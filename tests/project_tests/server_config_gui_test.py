#!/usr/bin/env python3
"""
简化的配置文件错误测试脚本
专门用于测试 tests/other_conf/error 目录下的所有配置文件(不包含子目录)
"""

from test_common_utils import setup_test_environment, setup_mock_messagebox, run_test_with_config_test_function,config_manager

# 设置测试环境
setup_test_environment()


def _config_file_test(config_path):
    """测试单个配置文件"""
    # 设置模拟messagebox
    setup_mock_messagebox()
    
    try:
        # 导入webhook_server_gui并运行
        from webhook_server import webhook_server_gui

        # 运行主程序
        config_manager.main_gui_load_config(config_path)
        webhook_server_gui.WebhookServerGUI(config_path)  # 一旦出现界面,就表示代码运行错误配置正常,表示代码存在问题

        return False, "程序正常启动"
        
    except Exception as e:
        error_msg = f"捕获到异常: {str(e)}"
        return True, error_msg

def main():
    """主函数"""
    # 使用公共函数运行测试，不包含子目录
    run_test_with_config_test_function(_config_file_test, include_subdirs=False)

if __name__ == "__main__":
    main()
