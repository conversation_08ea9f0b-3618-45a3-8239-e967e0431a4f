# 说明

> 简单来说,本项目是实现对指定IP下对其他设备数据的暂存和第三方设备读取数据,相当于一个数据中间商
> 本项目其当前是被当作局域网webhook服务端使用;
> 本文档是该项目的测试说明文档,包含单个功能点和整体流程的测试

## 杂项说明

### 项目结构

> `data`目录是重放服务端接收数据文件存放目录,不一定是该目录,其具体有效配置是在配置文件中配置项决定
> `doc`是存放本项目中说明文件和测试文档的目录
> `logs`是运行时不同级别日志存放目录,由配置文件中配置项决定
> `resources`是存放本项目中配置文件的目录,目前有webhook配置文件和log配置文件
> `src`目录是主体代码文件,其中`webhook_server.py`是运行主体文件,`webhook_server.models`子目录是存放自定义类的,`webhook_server.utils`目录是存放各种功能工具类的
> `tests`就是存放所有测试使用到的代码文件目录

### 基础简单类

> 这些类其中涉及到的逻辑简单,可以说是没有逻辑,故此这些类不需要进行测试,其包含:
> `constants`,`server_data.SendData`,`server_data.MessageResponse`,`server_data.TokenException`,
`server_state.ServerState`,`sql_log`
> 其中`server_state.ServerState`类存储的运行时服务端状态和配置属性和结束时的操作方法
> `sql_log`文件中则是在执行sql时在日志中打印具体执行的sql内容,其代码有效性可以在具体执行和测试中可以验证不需要单独设置测试用例

## 代码功能点说明及测试

### 配置项相关功能点

> 由于在`python`中对配置文件的解析,`ini`格式较为合适,后期已经将配置文件转换成了`server_config.ini`和`log.ini`;
> `server_config.ini`文件中包含服务端配置参数`[server]`和对应的发信方设备标识信息`[client_info]`;
> `webhook_server.config.constants`类中的`SERVER_REQUIRED_KEYS`字段表示的`[server]`中不可以缺失的配置项参数,不可缺失项包含:
`"api_key", "whitelist","message_data_table_name", "log_config_path","host", "port", "run_time", "time_zone", "expire_data_days", "data_limit_num"`

#### 配置文件解析和转换函数

```python
    @staticmethod
def _check_reset_server_config(server_config: dict):
    missing = constants.SERVER_REQUIRED_KEYS - server_config.keys()
    if missing:
        raise ValueError(f"server properties [server] missing required keys: {missing}")
    ...

    # 重新赋值[后续如果新增或者修改配置项,这里也需要更新]
    ...
    server_config["data_limit_num"] = data_limit_num

```

> `_check_reset_server_config`函数是测试获取服务端配置项参数并转换成对应格式的函数
- - -

#### 配置项参数功能性测试

##### 配置文件内容格式要求

1. 配置文件中`[server]`和`[client_info]`节点下必须存在内容

2. `SERVER_REQUIRED_KEYS`所要求的参数项在配置文件中必须存在

3. 参数项格式：
   3.1 `api_key`配置项存在长度要求,其必须超过10且由大小写字母和数字组成
   3.2 `whitelist`配置项中是多个ip或者IP段组成,其中多个IP之间必须以英文逗号分隔

   3.3  `log_config_path`必须是文件路径且`log_config_path`对应的文件路径必须存在

   3.4 `host`对应字符串是一个有效的主机IP
   3.5 `port`对应端口必须是一个在1-6635之间的有效数字,满足基础的端口要求
   3.6 `run_time`格式是`start_time-end_time`,其中`start_time`和`end_time`都必须是有效的表示24小时制的字符串,如果两者相等则说明24小时不停机
   3.7 `time_zone`要求其对应字符串可以解析成`ZoneInfo`类型
   3.8 `expire_data_days`和`data_limit_num`则要求其可以是数字即可

> `_check_reset_server_config`函数已经对文件配置项格式和必要项进行校验,而在`_parse_properties`
> 函数中校验了两个节点不能为空!

```python
    def _parse_properties(self):
    webhook_server.config = configparser.ConfigParser(interpolation=None)
    webhook_server.config.read(self.server_properties_path, encoding="utf-8")
    if not webhook_server.config.has_section('server'):
        raise ValueError("server properties file missing [server] section in server properties file!")
    if not webhook_server.config.has_section('client_info'):
        raise ValueError("server properties file missing [client_info] section in server properties file!")

    ...

    self.server_config = server_properties
    self.client_info_properties = client_info_properties
```

> 对格式的验证的测试代码用例在`test_server_properties.py`代码文件中

- - -

##### api_key使用

> `api_key`使用在获取临时的token中,用于校验是否可以获取`token`

```python
    try:
        scheme, api_key = authorization.split()
        if scheme.lower() != "bearer":
            raise HTTPException(status.HTTP_403_FORBIDDEN)
    except ValueError:
        logger.exception("get token url parse api-key error!")
        raise HTTPException(status.HTTP_403_FORBIDDEN)

    if api_key != web_server_state.properties.server_config["api_key"]:
        logger.error(f"this request api key invalid from {request.client.host}!")
        raise HTTPException(status.HTTP_403_FORBIDDEN)
```

> <b>api_key有效使用测试内容:</b>根据无效的或者不正确的`api_key`获取不到`token`,且正确的`api_key`获取到的`token`
> 必须可以在获取未读数据的接口中

- - -

##### whitelist-安全接口使用限制

> 在对安全有要求的接口中进行白名单校验,目前是token获取接口和未读数据读取接口;
> 统一是在前置校验函数中使用

```python
client_ip = request.client.host
# 需要进行进行校验的接口
api_interfaces = ["/webhook/token", "/webhook/unread"]
whitelist = web_server_state.properties.server_config["whitelist"]
if request.url.path in api_interfaces and not server_utils.is_ip_in_whitelist(client_ip, whitelist):
    logger.error(f"this ip:{client_ip} not allow request!")
    return JSONResponse({"error": "IP not allowed"}, status_code=403)
```

- - -

##### 数据保存文件

> 该`message_data_table_name`配置项是指定数据库sqlite数据库文件中对应的数据存储表名,在对应文件不存在时创建该库表文件和表,有时则直接进行下一步,进行数据库操作
> 安全提示: 如果要求高的话,可以进行数据库文件定时定点备份,其在配置文件中的配置项使用全路径

```python
class DataManager:
    def __init__(self, db_path: str, scheduler: BaseScheduler, zone: ZoneInfo = None):
        self.db_path = os.path.abspath(db_path)
        ...
        self._init_db()
        ...

    # 初始化数据库表结构并设置WAL/NORMAL模式
    def _init_db(self):
        os.makedirs(os.path.dirname(self.db_path), exist_ok=True)
        # 初始化数据库:[新建表,创建索引,设置WAL/NORMAL模式]
        # , factory=sql_log.LoggingConnection --- 打印具体执行的SQL语句
        with sqlite3.connect(self.db_path) as conn:
            conn.execute("PRAGMA journal_mode=WAL;")
            conn.execute("PRAGMA synchronous=NORMAL;")
            init_sql = """
                CREATE TABLE IF NOT EXISTS 'messages' (
                    ...
                );
                CREATE INDEX IF NOT EXISTS ...;
                """.format(min_message_len=constants.MIN_MESSAGE_LEN, max_message_len=constants.MAX_MESSAGE_LEN,
                           min_client_key_len=constants.MIN_CLIENT_KEY_LEN,
                           max_client_key_len=constants.MAX_CLIENT_KEY_LEN)
            conn.executescript(init_sql)
            conn.commit()
```

---

##### log配置文件

> `log_config_path`配置项是log配置文件,其必须是一个存在的且以`.ini`结尾的文件,其内部是logger所需要的配置参数
> 包含log文件目录和log文件格式要求,log文件过期保存限制以及在控制台不同级别颜色显示
> 其中log时区设置则由server中配置项`time_zone`决定

```python
def configure(self, config_path: str, time_zone: ZoneInfo):
        if self._initialized:
            return
        self.webhook_server.config = configparser.ConfigParser(interpolation=None)
        self.webhook_server.config.read(config_path, encoding="utf-8")

        # 基础配置
        basic_log_config = self.webhook_server.config["log"]
        self.base_dir = os.path.abspath(basic_log_config.get("dir", "logs"))
        ...

        # 清理过期日志 [初始化时执行一次]
        self._clean_expired_logs()
        self._initialized = True
```

- - -

##### host-port

> 在经过格式的校验之后,上述配置项是启动web服务器所需要的参数项,其配置之后启动成功且打印显示的和配置项相同时则表示有效

```python
web_server_state.webhook_server = uvicorn.Server(uvicorn.Config(
                app, host=webhook_server.config["host"], port=webhook_server.config["port"],
                log_config=None, log_level=None, access_log=False
            ))
```

- - -

##### 时间段要求

1. 开始时间和结束时间一致则一直运行
2. 当开始时间大于结束时间时,即允许隔天允许
3. 当不在运行时间段范围内时,无法启动运行,且当运行到结束时间时马上停止服务端

```python
try:
    start_time, end_time = webhook_server.config["run_time"].split('-')
    start_time = datetime.strptime(start_time.strip(), "%H:%M").time()
    end_time = datetime.strptime(end_time.strip(), "%H:%M").time()
    now = datetime.now(web_server_state.properties.server_config_zone).time()
    # 开始时间和结束时间一致,则一直运行
    always_run = (start_time == end_time)
    # 支持跨天运行
    can_run = (always_run or (start_time <= now <= end_time)
               or (end_time < start_time and (now <= end_time or now >= start_time)))
    if can_run:
        # 补充关闭时触发的定时任务:其中的下一次执行时间需要考虑临界点[当前时间刚好是结束时间]
        if not always_run:
            web_server_state.properties.scheduler.add_job(cleanup_on_shutdown, trigger='cron', hour=end_time.hour,
                                                          minute=end_time.minute, id="cleanup_on_shutdown_job")

        web_server_state.webhook_server = uvicorn.Server(uvicorn.Config(
            app, host=webhook_server.config["host"], port=webhook_server.config["port"],
            log_config=None, log_level=None, access_log=False
        ))
        await web_server_state.webhook_server.serve()
```

> 以上代码片段满足上述要求,支持自定义指定时间段运行
- - -

##### 已读数据有效期/数据总数上限

> `expire_data_days`表示的是已读数据过期保存天数,其配置值如果是负数则表示永不过期
> `data_limit_num`表示最大数据条数,数据总数超过部分中的已读数据会被删除
> 上述两个配置项都是定时任务,会定期执行,且都是保证数据库文件整体不会太大

```python
    def remove_excess_read_data(self, data_limit_num: int):
    # 当所允许的数据库中条数是非正数时,则不限制数据数量
    if data_limit_num < 1:
        logger.warning(f"the specified data limit number is less than 1, no excess data will be removed")
        return
    if data_limit_num < constants.MAX_MESSAGE_COUNT:
        raise ValueError(f"data limit number must greater than {constants.MAX_MESSAGE_COUNT}")
    self._exec_retry(lambda cur: DataManager._remove_excess_read_data(cur, data_limit_num))


def remove_expired_read_data(self, expire_data_days):
    # 当所允许的数据库中过期时间是非正数时,则数据永不过期
    if expire_data_days < 1:
        logger.warning(f"the specified expire data days is less than 1, no expired data will be removed")
        return
    self._exec_retry(lambda c: DataManager._remove_expired_read_data(c, expire_data_days))


web_server_state.properties.scheduler.add_job(web_server_state.properties.data_manager.remove_excess_read_data,
                                              'interval', minutes=1, args=[webhook_server.config["data_limit_num"]],
                                              id="remove_excess_read_data_job")
web_server_state.properties.scheduler.add_job(web_server_state.properties.data_manager.remove_expired_read_data,
                                              'interval', minutes=1, args=[webhook_server.config["expire_data_days"]],
                                              id="remove_expired_read_data_job")        
```

### util代码功能要求及测试

> util功能可以是一个函数,也可以是一个大的代码文件;其目的是为了实现一个整体统一的要求目标;
> 这些代码实现都在`src\webhook_server.utils`目录中

#### self_log

> 该代码文件是实现自定义log配置要求的，目前的需求是分级将不同的级别的日志在控制台展示和记录在不同的log文件中,方便后期查询分析
> 其log配置文件在代码中都存在默认的配置项,如果在配置文件中不存在则使用默认项
> 测试用例需要有：
> 全使用默认项配置时默认项是否生效且没有发生意外报错情况
> 部分使用默认项时其默认项和配置文件中配置项使用都生效且都是对应的值
> 全部都是配置文件中的配置项时需要满足自定义配置的要求且生效的配置项值是对应对应自定义值
> 错误格式的配置项使用时一定报错不再执行后续代码
> 所有正向的流程测试在使用其`logger`时必须满足控制台日志显示和日志文件的记录

```python

```

- - -

#### server_data_manager

```python

```

- - -

#### server_utils

- - -

#### shutdown_exec_funct

```python

```

- - -

## 服务端功能流程测试

### 程序关闭时未读信息的保存和历史数据的覆盖

> 说明无论该程序是否是正常关闭还是程序崩溃,都必须进行未读信息的有效保存【通过系统内核的程序关闭当前无法拦截：如 kill -9】
