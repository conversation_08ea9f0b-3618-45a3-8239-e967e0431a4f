"""
通用型签到模板父类

提供标准化的签到流程，包括登录验证、cookie管理、签到状态检查等功能。
子类只需实现具体的业务逻辑方法即可。
"""

from abc import ABC, abstractmethod
from typing import Optional, Dict, Any, Tuple
import requests
import json
import os
import logging
import time
import random
from datetime import datetime
from pathlib import Path
from zoneinfo import ZoneInfo


class BaseCheckIn(ABC):
    """通用型签到模板父类
    
    提供标准化的签到流程，包括登录验证、cookie管理、签到状态检查等功能。
    子类只需实现具体的业务逻辑方法即可。
    
    Attributes:
        username (str): 用户名
        password (str): 密码
        session (requests.Session): HTTP会话对象
        cookie_file_path (str): cookie文件保存路径
        logger (logging.Logger): 日志记录器
        log_file_path (str): 日志文件路径
        log_level (int): 日志级别
        request_timeout (int): 请求超时时间（秒）
        headless (bool): 自动化操作是否可视化（True为无头模式，False为可视化）
        timezone (ZoneInfo): 时区设置
        is_logged_in (bool): 当前登录状态
        last_check_in_time (Optional[datetime]): 上次签到时间
        login_method (Optional[str]): 当前登录方式（cookie/credentials）
    """
    
    def __init__(self,
                 username: str,
                 password: str,
                 cookie_file_path: Optional[str] = None,
                 log_file_path: Optional[str] = None,
                 log_level: int = logging.INFO,
                 request_timeout: int = 30,
                 headless: bool = True,
                 timezone: Optional[ZoneInfo] = None):
        """初始化签到类

        Args:
            username: 用户名
            password: 密码
            cookie_file_path: cookie文件保存路径，默认为None时自动生成
            log_file_path: 日志文件路径，默认为None时自动生成
            log_level: 日志级别，默认为INFO
            request_timeout: 请求超时时间（秒），默认30秒
            headless: 自动化操作是否可视化，True为无头模式，False为可视化，默认True
            timezone: 时区设置，默认为None时使用系统时区
        """
        # 核心属性
        self.username = username
        self.password = password
        self.session = requests.Session()
        self.session.timeout = request_timeout

        # 配置属性
        self.log_level = log_level
        self.request_timeout = request_timeout
        self.headless = headless
        self.timezone = timezone or ZoneInfo("Asia/Shanghai")  # 默认使用上海时区
        
        # 文件路径配置
        self.cookie_file_path = cookie_file_path or f"cookies_{username}.json"
        # 使用指定时区生成日志文件名
        current_time = datetime.now(self.timezone)
        self.log_file_path = log_file_path or f"check_in_{username}_{current_time.strftime('%Y%m%d')}.log"
        
        # 状态属性
        self.is_logged_in = False
        self.last_check_in_time = None
        self.login_method = None
        
        # 初始化日志记录器
        self.logger = self._setup_logger()
        
        self.logger.info(f"initialized check-in instance for user: {username}")
    
    def _setup_logger(self) -> logging.Logger:
        """设置日志记录器 - 同时输出到控制台和文件"""
        logger_name = f"check_in_{self.__class__.__name__}_{self.username}"
        logger = logging.getLogger(logger_name)
        
        # 避免重复添加处理器
        if logger.handlers:
            return logger
            
        logger.setLevel(self.log_level)
        
        # 创建格式化器（使用指定时区）
        class TimezoneFormatter(logging.Formatter):
            def __init__(self, fmt, datefmt, timezone):
                super().__init__(fmt, datefmt)
                self.timezone = timezone

            def formatTime(self, record, datefmt=None):
                dt = datetime.fromtimestamp(record.created, tz=self.timezone)
                if datefmt:
                    return dt.strftime(datefmt)
                else:
                    return dt.strftime('%Y-%m-%d %H:%M:%S %Z')

        formatter = TimezoneFormatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S %Z',
            timezone=self.timezone
        )
        
        # 控制台处理器
        console_handler = logging.StreamHandler()
        console_handler.setLevel(self.log_level)
        console_handler.setFormatter(formatter)
        logger.addHandler(console_handler)
        
        # 文件处理器
        try:
            # 确保日志目录存在
            log_dir = Path(self.log_file_path).parent
            log_dir.mkdir(parents=True, exist_ok=True)
            
            file_handler = logging.FileHandler(self.log_file_path, encoding='utf-8')
            file_handler.setLevel(self.log_level)
            file_handler.setFormatter(formatter)
            logger.addHandler(file_handler)
        except Exception as e:
            # 如果文件处理器创建失败，只使用控制台输出
            print(f"warning: failed to create file handler for {self.log_file_path}: {e}")
        
        return logger
    
    # ========== 抽象方法 - 子类必须实现 ==========
    
    @abstractmethod
    def is_login_successful(self) -> bool:
        """判断登录是否成功
        
        子类需要实现具体的登录成功判断逻辑，例如：
        - 检查特定页面元素是否存在
        - 验证API响应状态
        - 检查用户信息是否正确获取
        
        Returns:
            bool: 登录成功返回True，否则返回False
        """
        pass
    
    @abstractmethod
    def login_with_credentials(self) -> bool:
        """使用账户和密码登录
        
        子类需要实现具体的账密登录逻辑，例如：
        - 发送登录请求
        - 处理验证码
        - 处理登录表单提交
        
        Returns:
            bool: 登录成功返回True，否则返回False
        """
        pass
    
    @abstractmethod
    def login_with_cookie(self) -> bool:
        """尝试使用cookie登录
        
        子类需要实现具体的cookie登录逻辑，例如：
        - 访问需要登录的页面
        - 验证cookie有效性
        - 检查登录状态
        
        Returns:
            bool: 登录成功返回True，否则返回False
        """
        pass
    
    @abstractmethod
    def is_already_checked_in(self) -> bool:
        """判断是否已经签到
        
        子类需要实现具体的签到状态检查逻辑，例如：
        - 检查签到页面状态
        - 查询签到记录API
        - 解析页面签到信息
        
        Returns:
            bool: 已签到返回True，否则返回False
        """
        pass
    
    @abstractmethod
    def perform_check_in(self) -> bool:
        """执行签到操作（假设已登录成功）

        子类需要实现具体的签到操作逻辑，例如：
        - 点击签到按钮
        - 发送签到API请求
        - 处理签到结果

        Returns:
            bool: 签到成功返回True，否则返回False
        """
        pass

    # ========== 具体实现方法 ==========

    def save_cookies(self) -> None:
        """保存当前session的cookies到文件"""
        try:
            cookies_dict = {}
            for cookie in self.session.cookies:
                cookies_dict[cookie.name] = cookie.value

            # 确保cookie目录存在
            cookie_dir = Path(self.cookie_file_path).parent
            cookie_dir.mkdir(parents=True, exist_ok=True)

            with open(self.cookie_file_path, 'w', encoding='utf-8') as f:
                json.dump(cookies_dict, f, ensure_ascii=False, indent=2)

            self.logger.info(f"cookies saved to {self.cookie_file_path}")
        except Exception as e:
            self.logger.error(f"failed to save cookies: {e}")

    def load_cookies(self) -> bool:
        """从文件加载cookies到session

        Returns:
            bool: 加载成功返回True，否则返回False
        """
        try:
            if not os.path.exists(self.cookie_file_path):
                self.logger.info(f"cookie file {self.cookie_file_path} not found")
                return False

            with open(self.cookie_file_path, 'r', encoding='utf-8') as f:
                cookies_dict = json.load(f)

            for name, value in cookies_dict.items():
                self.session.cookies.set(name, value)

            self.logger.info(f"cookies loaded from {self.cookie_file_path}")
            return True
        except Exception as e:
            self.logger.error(f"failed to load cookies: {e}")
            return False

    def check_in(self) -> Tuple[bool, str]:
        """通用型签到方法 - 完整的签到流程

        执行流程：
        1. 尝试使用cookie登录
        2. 如果cookie登录失败，使用账密登录
        3. 如果账密登录也失败，停止操作
        4. 登录成功后检查是否已签到
        5. 如果已签到，直接返回成功
        6. 如果未签到，随机等待20-30秒（模拟人工操作）
        7. 执行签到操作

        Returns:
            Tuple[bool, str]: (是否成功, 结果消息)
        """
        self.logger.info(f"starting check-in process for user: {self.username}")

        # 步骤1: 尝试使用cookie登录
        login_success = False

        if self.load_cookies():
            self.logger.info("attempting login with cookies")
            try:
                if self.login_with_cookie() and self.is_login_successful():
                    login_success = True
                    self.login_method = "cookie"
                    self.is_logged_in = True
                    self.logger.info("cookie login successful")
                else:
                    self.logger.info("cookie login failed, will try credentials")
            except Exception as e:
                self.logger.error(f"cookie login error: {e}")

        # 步骤2: 如果cookie登录失败，使用账密登录
        if not login_success:
            self.logger.info("attempting login with credentials")
            try:
                if self.login_with_credentials() and self.is_login_successful():
                    login_success = True
                    self.login_method = "credentials"
                    self.is_logged_in = True
                    self.logger.info("credentials login successful")
                    # 登录成功后保存cookies
                    self.save_cookies()
                else:
                    self.logger.error("credentials login failed")
            except Exception as e:
                self.logger.error(f"credentials login error: {e}")

        # 步骤3: 如果都登录失败，停止操作
        if not login_success:
            error_msg = "login failed with both cookie and credentials"
            self.logger.error(error_msg)
            return False, error_msg

        # 步骤4: 检查是否已签到
        try:
            if self.is_already_checked_in():
                success_msg = f"already checked in today (login method: {self.login_method})"
                self.logger.info(success_msg)
                return True, success_msg
        except Exception as e:
            self.logger.error(f"failed to check sign-in status: {e}")
            return False, f"failed to check sign-in status: {e}"

        # 步骤5: 随机等待后执行签到操作
        try:
            # 登录成功但没有签到，随机等待20-30秒
            wait_time = random.uniform(20, 30)
            self.logger.info(f"login successful but not checked in yet, waiting {wait_time:.2f} seconds before check-in")
            time.sleep(wait_time)

            if self.perform_check_in():
                self.last_check_in_time = datetime.now(self.timezone)
                success_msg = f"check-in successful after {wait_time:.2f}s wait (login method: {self.login_method})"
                self.logger.info(success_msg)
                return True, success_msg
            else:
                error_msg = "check-in operation failed"
                self.logger.error(error_msg)
                return False, error_msg
        except Exception as e:
            error_msg = f"check-in operation error: {e}"
            self.logger.error(error_msg)
            return False, error_msg

    def cleanup(self) -> None:
        """清理资源"""
        try:
            if self.session:
                self.session.close()
                self.logger.info("session closed")

            # 关闭日志处理器
            for handler in self.logger.handlers[:]:
                handler.close()
                self.logger.removeHandler(handler)

        except Exception as e:
            print(f"cleanup error: {e}")

    def __enter__(self):
        """上下文管理器入口"""
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器退出"""
        self.cleanup()
