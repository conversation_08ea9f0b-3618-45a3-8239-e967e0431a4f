"""
webhook server gui 主界面 --- 问题修复 --- 网络通信修复 菜单项 子界面
"""
from tkinter import messagebox

from common.utils import logging_utils
from webhook_server.models import gui_properties
from webhook_server.utils import webhook_gui_utils


class NetworkFixDialogComponent:

    def __init__(self, props: gui_properties.GUIProperties):
        """初始化网络问题修复对话框组件。

        Args:
            props: GUI属性
        """
        self.props = props

    def open_fix_network_dialog(self):
        """提示是否进行网络通信修复 --- 对话框界面 """
        logging_utils.logger_print(msg="network communication fix requested", custom_logger=self.props.logger)

        # 检测当前服务端是否启动
        if self.props.server_process_alive():
            logging_utils.logger_print(msg="server is running, cannot perform network fix", custom_logger=self.props.logger)
            messagebox.showwarning(
                title="警告",
                message="不能在服务端启动时进行网络修复，请先停止服务端后再尝试。",
                parent=self.props.root
            )
            return

        logging_utils.logger_print(msg="server is not running, proceeding with network fix", custom_logger=self.props.logger)
        # 服务端未启动，调用网络修复函数
        try:
            webhook_gui_utils.restore_network_connection(self.props.root)
            logging_utils.logger_print(msg="network communication fix completed successfully", custom_logger=self.props.logger)
        except Exception as e:
            logging_utils.logger_print(msg="network communication fix failed", custom_logger=self.props.logger, use_exception=True, exception=e)
            messagebox.showerror(
                title="错误",
                message=f"网络通信修复失败：{str(e)}",
                parent=self.props.root
            )
