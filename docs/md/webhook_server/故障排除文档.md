# 🔧 NexusRecv 故障排除文档

[![Support](https://img.shields.io/badge/support-community-blue.svg)](README.md)
[![Platform](https://img.shields.io/badge/platform-Windows%20%7C%20Linux%20%7C%20macOS-lightgrey.svg)](README.md)
[![Version](https://img.shields.io/badge/version-v1.0.2-green.svg)](README.md)

## 📋 概述

本文档提供NexusRecv常见问题的解决方案和故障排除指南，帮助用户快速定位和解决使用过程中遇到的问题。

### 🎯 文档结构
- **🚨 常见问题**: 用户最常遇到的问题及解决方案
- **🔍 诊断工具**: 问题诊断和分析工具
- **📝 日志分析**: 日志文件分析和问题定位
- **🛠️ 修复方法**: 具体的问题修复步骤
- **📞 技术支持**: 获取进一步帮助的渠道

## 🚨 常见问题及解决方案

### 🌐 网络连接问题

#### ❌ 问题1: 无法启动服务器

**症状**:
- 点击"启动服务器"按钮无响应
- 界面显示"服务器启动失败"
- 日志中出现端口占用错误

**可能原因**:
1. 配置的端口已被其他程序占用
2. 防火墙阻止了程序网络访问
3. IP地址配置错误
4. 权限不足

**解决方案**:

**步骤1: 检查端口占用**
```bash
# Windows
netstat -ano | findstr :8000

# Linux/macOS
netstat -tulpn | grep :8000
lsof -i :8000
```

**步骤2: 更换端口**
```ini
# 在配置文件中修改端口
[server]
port = 8001  # 使用其他未占用端口
```

**步骤3: 检查防火墙设置**
```bash
# Windows - 允许程序通过防火墙
# 控制面板 > 系统和安全 > Windows Defender 防火墙 > 允许应用通过防火墙

# Linux - 开放端口
sudo ufw allow 8000
sudo firewall-cmd --permanent --add-port=8000/tcp
```

**步骤4: 使用网络修复功能**
- 在GUI界面点击"菜单" > "网络通信修复"
- 按照提示进行网络诊断和修复

#### ❌ 问题2: 设备无法连接到服务器

**症状**:
- 设备发送数据返回连接超时
- API调用返回404或连接拒绝
- 服务器日志中没有收到请求记录

**诊断步骤**:

**步骤1: 验证网络连通性**
```bash
# 从设备端测试服务器连通性
ping *************

# 测试端口连通性
telnet ************* 8000

# 使用curl测试API
curl -v http://*************:8000/docs
```

**步骤2: 检查IP白名单配置**
```ini
[server]
# 确保设备IP在白名单中
whitelist = ***********/24,设备IP地址
```

**步骤3: 验证服务器状态**
- 确认GUI界面显示"服务器运行中"
- 检查服务器监听地址是否为0.0.0.0
- 验证端口配置是否正确

### ⚙️ 配置文件问题

#### ❌ 问题3: 配置文件加载失败

**症状**:
- 程序启动时提示"配置文件错误"
- 界面显示配置验证失败
- 无法保存配置更改

**常见配置错误**:

**错误1: 文件编码问题**
```
错误信息: UnicodeDecodeError: 'gbk' codec can't decode
解决方案: 确保配置文件使用UTF-8编码保存
```

**错误2: INI格式错误**
```ini
# ❌ 错误格式
[server
api_key = test

# ✅ 正确格式
[server]
api_key = test
```

**错误3: 缺少必填项**
```
错误信息: Missing required field: api_key
解决方案: 添加缺少的配置项
```

**修复步骤**:

**步骤1: 验证文件编码**
```bash
# 检查文件编码
file -i config.ini

# 转换编码（如果需要）
iconv -f GBK -t UTF-8 config.ini > config_utf8.ini
```

**步骤2: 使用配置验证工具**
```python
# 验证配置文件
python -c "
from webhook_server.config import config_check
try:
    config_check.common_check_config_file('config.ini')
    print('✅ 配置文件验证通过')
except Exception as e:
    print(f'❌ 配置文件错误: {e}')
"
```

**步骤3: 使用示例配置**
```bash
# 复制示例配置文件
cp resources/server_config.ini my_config.ini
# 根据需要修改配置项
```

#### ❌ 问题4: 设备标识配置错误

**症状**:
- 设备发送数据返回"Client key not found"
- API调用返回403 Forbidden
- 设备在GUI中显示为"未知设备"

**解决方案**:

**步骤1: 检查设备标识格式**
```ini
[client_info]
# ❌ 错误格式（长度不足、包含特殊字符）
dev1 = 设备1
device-001 = 设备2

# ✅ 正确格式
device001 = 设备1
sensor001 = 传感器1
```

**步骤2: 验证设备标识要求**
- 长度: 10-30个字符
- 字符集: 仅字母和数字
- 大小写敏感

**步骤3: 重启服务器**
- 修改配置后需要重启服务器才能生效

### 💾 数据存储问题

#### ❌ 问题5: 数据库操作失败

**症状**:
- 数据发送成功但无法查询
- 界面显示数据库错误
- 日志中出现SQLite错误

**诊断步骤**:

**步骤1: 检查数据库文件权限**
```bash
# 检查数据库文件权限
ls -la *.db

# 修改权限（如果需要）
chmod 644 *.db
```

**步骤2: 验证数据库完整性**
```bash
# 使用SQLite命令行工具检查
sqlite3 database.db ".schema"
sqlite3 database.db "PRAGMA integrity_check;"
```

**步骤3: 清理数据库**
```python
# 使用清理脚本
python scripts/cleanup_db.py
```

#### ❌ 问题6: 磁盘空间不足

**症状**:
- 数据保存失败
- 日志文件无法写入
- 系统运行缓慢

**解决方案**:

**步骤1: 检查磁盘空间**
```bash
# Windows
dir C:\ /-c

# Linux/macOS
df -h
```

**步骤2: 清理过期数据**
```ini
[server]
# 设置较短的数据保存期
expire_data_days = 3
# 限制数据存储数量
data_limit_num = 10000
```

**步骤3: 清理日志文件**
```bash
# 清理旧日志文件
find logs/ -name "*.log" -mtime +7 -delete
```

### 🖥️ GUI界面问题

#### ❌ 问题7: 界面无法启动

**症状**:
- 双击程序无响应
- 出现Python错误对话框
- 命令行显示导入错误

**解决方案**:

**步骤1: 检查Python环境**
```bash
# 验证Python版本
python --version

# 检查必要模块
python -c "import tkinter, ttkbootstrap, fastapi"
```

**步骤2: 重新安装依赖**
```bash
pip install -r requirements.txt --force-reinstall
```

**步骤3: 使用命令行模式**
```bash
# 如果GUI无法启动，使用命令行模式
python src/webhook_server/webhook_server_command.py --config config.ini
```

#### ❌ 问题8: 系统托盘功能异常

**症状**:
- 最小化到托盘失败
- 托盘图标不显示
- 托盘菜单无响应

**解决方案**:

**步骤1: 检查系统托盘设置**
```
Windows: 设置 > 个性化 > 任务栏 > 选择哪些图标显示在任务栏上
Linux: 确保桌面环境支持系统托盘
```

**步骤2: 重启程序**
- 完全退出程序后重新启动
- 检查是否有多个实例在运行

**步骤3: 禁用托盘功能**
- 如果托盘功能持续异常，可以不使用最小化到托盘功能

## 🔍 诊断工具

### 📊 系统信息检查

```python
# 运行系统环境检查
python scripts/build/test_build_env.py
```

**检查项目**:
- Python版本和路径
- 操作系统信息
- 关键文件存在性
- 依赖模块安装情况
- 网络连接状态

### 🌐 网络诊断工具

```bash
# 网络连通性测试脚本
#!/bin/bash

echo "🔍 网络诊断开始..."

# 检查本地网络接口
echo "📡 网络接口信息:"
ip addr show  # Linux
ipconfig      # Windows

# 检查端口监听状态
echo "🔌 端口监听状态:"
netstat -tulpn | grep :8000

# 测试API接口
echo "🌐 API接口测试:"
curl -v http://localhost:8000/docs

echo "✅ 网络诊断完成"
```

### 📝 日志分析工具

```python
# 日志分析脚本
import re
from pathlib import Path

def analyze_logs(log_dir="logs"):
    """分析日志文件，提取关键错误信息"""
    log_path = Path(log_dir)
    
    error_patterns = [
        r"ERROR.*",
        r"CRITICAL.*",
        r"Exception.*",
        r"Failed.*",
        r"Connection.*refused",
        r"Permission.*denied"
    ]
    
    for log_file in log_path.glob("*.log"):
        print(f"📄 分析日志文件: {log_file}")
        
        with open(log_file, 'r', encoding='utf-8') as f:
            for line_num, line in enumerate(f, 1):
                for pattern in error_patterns:
                    if re.search(pattern, line, re.IGNORECASE):
                        print(f"  ⚠️  行 {line_num}: {line.strip()}")

if __name__ == "__main__":
    analyze_logs()
```

## 📝 日志分析指南

### 📂 日志文件位置

```
logs/
├── {app_name}-critical-{date}.log    # 严重错误日志
├── {app_name}-error-{date}.log       # 错误日志
├── {app_name}-warning-{date}.log     # 警告日志
├── {app_name}-info-{date}.log        # 信息日志
└── {app_name}-debug-{date}.log       # 调试日志
```

### 🔍 关键错误模式

| 错误类型 | 日志模式 | 可能原因 |
|----------|----------|----------|
| **网络错误** | `Connection refused`, `Timeout` | 网络连接问题、防火墙阻止 |
| **配置错误** | `Config error`, `Invalid format` | 配置文件格式或内容错误 |
| **权限错误** | `Permission denied`, `Access denied` | 文件权限不足 |
| **数据库错误** | `SQLite error`, `Database locked` | 数据库文件问题 |
| **认证错误** | `Invalid token`, `Unauthorized` | API密钥或令牌问题 |

### 📊 日志级别说明

| 级别 | 说明 | 处理建议 |
|------|------|----------|
| **CRITICAL** | 严重错误，程序无法继续运行 | 立即处理 |
| **ERROR** | 错误，功能无法正常工作 | 优先处理 |
| **WARNING** | 警告，可能影响功能 | 关注并计划处理 |
| **INFO** | 信息，正常运行状态 | 用于监控和分析 |
| **DEBUG** | 调试信息，详细执行过程 | 开发调试使用 |

## 🛠️ 高级故障排除

### 🔧 多实例冲突问题

**症状**:
- 配置文件被锁定
- 数据库访问冲突
- 端口占用冲突

**解决方案**:

**步骤1: 检查运行实例**
```bash
# Windows
tasklist | findstr python

# Linux/macOS
ps aux | grep python
```

**步骤2: 清理进程锁**
```python
# 清理配置锁文件
python scripts/cleanup_directories.py
```

**步骤3: 使用不同配置**
- 为每个实例使用不同的配置文件
- 确保端口、表名、应用名唯一

### 🔄 性能问题诊断

**症状**:
- 响应速度慢
- 内存使用过高
- CPU占用率高

**诊断步骤**:

**步骤1: 监控系统资源**
```python
import psutil

# 监控CPU和内存使用
def monitor_resources():
    cpu_percent = psutil.cpu_percent(interval=1)
    memory = psutil.virtual_memory()
    
    print(f"CPU使用率: {cpu_percent}%")
    print(f"内存使用率: {memory.percent}%")
    print(f"可用内存: {memory.available / 1024 / 1024:.1f} MB")
```

**步骤2: 优化配置**
```ini
[server]
# 减少数据保存期
expire_data_days = 3
# 限制数据数量
data_limit_num = 10000
# 关闭SQL日志
enable_sql_logging = false
```

**步骤3: 清理数据**
```bash
# 定期清理过期数据
python scripts/cleanup_db.py
```

## 📞 获取技术支持

### 🆘 自助解决

1. **📚 查阅文档**: 
   - [快速使用指南](快速使用指南.md)
   - [配置文件说明](配置文件说明.md)
   - [API接口文档](API接口文档.md)

2. **🔍 搜索问题**: 在GitHub Issues中搜索类似问题

3. **🧪 测试环境**: 在测试环境中复现问题

### 💬 社区支持

1. **🐛 GitHub Issues**: [提交问题报告](https://github.com/your-repo/nexusrecv/issues)
2. **💡 GitHub Discussions**: [参与社区讨论](https://github.com/your-repo/nexusrecv/discussions)
3. **📧 邮件支持**: <EMAIL>

### 📋 问题报告模板

提交问题时，请提供以下信息：

```markdown
## 问题描述
简要描述遇到的问题

## 环境信息
- 操作系统: Windows 10 / Linux Ubuntu 20.04 / macOS
- Python版本: 3.11.x
- NexusRecv版本: v1.0.2
- 安装方式: 源码安装 / 可执行文件

## 复现步骤
1. 第一步操作
2. 第二步操作
3. 出现问题

## 期望结果
描述期望的正常行为

## 实际结果
描述实际发生的异常行为

## 错误日志
```
粘贴相关的错误日志
```

## 配置文件
```ini
# 粘贴相关的配置内容（注意隐藏敏感信息）
```

## 其他信息
任何其他可能有助于解决问题的信息
```

---

<div align="center">

**🔧 持续改进**

我们持续改进产品质量和用户体验。如果您发现新的问题或有改进建议，欢迎通过GitHub Issues或邮件联系我们。

**📚 相关文档**

[快速使用指南](快速使用指南.md) | [配置文件说明](配置文件说明.md) | [API接口文档](API接口文档.md) | [开发者文档](开发者文档.md)

</div>
