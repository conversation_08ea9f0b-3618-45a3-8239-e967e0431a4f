# 单机桌面应用程序多开配置管理方案设计文档
## 应用场景说明

### 应用类型
- **单机桌面应用程序**：运行在本地计算机上的GUI应用程序
- **支持多开**：允许同时运行多个应用实例，每个实例使用独立的配置文件
- **配置隔离**：每个应用实例必须使用不同的配置，确保实例间互不干扰

### 典型使用场景
1. **多账户管理**：不同配置对应不同的服务账户或API密钥
2. **A/B测试**：同时运行不同参数配置进行对比测试
## 场景条件分类

### 必要条件（AND条件）
- **首次运行场景**：
  - 数据库配置表为空
  - 无任何历史配置记录
> 首次运行的场景中，不加载配置选择界面，直接进入主界面，用户在主界面的"设置"菜单中配置服务端参数  

- **配置有效且可用场景**：
  - 配置记录在数据库中存在
  - 配置文件实际存在
  - 配置文件可读（有访问权限）
  - 配置未被其他进程占用
  - 配置文件哈希与数据库记录一致
  - 配置文件内配置项完整且格式正确
  - 配置文件内配置项多进程唯一
> 加载配置选择界面，用户选择已有的有效配置进入主界面，或创建新配置  

### 可选条件（OR条件）
- **配置不可用场景**：
  - 配置文件被删除（路径无效）
  - 配置文件被外部修改（哈希不匹配）
  - 配置文件无读取权限
  - 配置被其他进程占用[有效但不可用]
  - 配置文件内配置项不完整或者格式不正确
  - 配置文件内配置项多进程不唯一
> 当所有配置记录无效或者有效不可用时,就只能查看当前界面,该界面不能选择配置,只能和首次运行的场景一样选择进入主界面新建配置 

## 配置加载选择流程

```mermaid
graph TD
    %% 程序启动流程
    A[程序启动] --> B{数据库存在配置?}
    B -->|否| C[进入主界面新建配置]
    B -->|是| D[加载所有配置记录]
    
    %% 配置验证流程
    D --> E[验证每条配置有效性]
    E --> F{配置有效?}
    F -->|是| G{未被占用?}
    G -->|是| H[加入可选列表]
    G -->|否| I[加入占用列表]
    F -->|否| J[加入无效列表]
    
    %% 配置选择界面
    H --> K[显示配置选择界面]
    I --> K
    J --> K
    
    %% 界面操作
    K -->|刷新配置| D
    K -->|新建配置| C
    K -->|清除无效配置| M[删除所有无效配置]
    M --> D
    K -->|选择配置| N[选中可用配置]
    K -->|导入配置| Q[选择配置文件]
    
    %% 配置加载流程
    N --> O{实时验证配置有效性}
    O -->|有效| P[加载配置进入主界面]
    O -->|无效| T[显示错误信息]
    T --> K
    
    %% 配置导入流程
    Q --> R{验证文件有效性}
    R -->|有效| S[保存配置到数据库]
    R -->|无效| U[显示错误信息]
    U --> K
    S --> P
    
    %% 流程终点
    C --> X[主界面运行]
    P --> X
    K -->|退出程序| Z[结束]
    X --> Z
```

## 配置选择界面功能涉及

### 布局设计
```
+------------------------------------------------------+
|              配置选择                                 |
+------------------------------------------------------+
| [新增配置] [导入配置]  [清理无效] [刷新列表]               |
| 🟢 可用配置 (2个)                                     |
| ┌─────────────────────────────────────────────────┐  |
| │ ○ 办公室配置      最后使用: 2小时前  [选择]           │  |
| │   📁 /Users/<USER>/configs/production.ini          │  |
| │                                                 │  |
| │ ○ 测试环境配置    最后使用: 1天前   [选择]           │  |
| │   📁 /Users/<USER>/configs/testing.ini             │  |
| └─────────────────────────────────────────────────┘  |
|                                                      |
| 🔴 占用配置 (1个)                                      |
| ┌─────────────────────────────────────────────────┐  |
| │ ✗ 开发环境配置    占用进程: PID 1234               │   |
| │   📁 /Users/<USER>/configs/development.ini        │   |
| │   ⏰ 占用时间: 30分钟前                           │   |
| └─────────────────────────────────────────────────┘   |
|                                                       |
| ⚠️ 无效配置 (2个)                                       |
| ┌─────────────────────────────────────────────────┐   |
| │ ❌ 预发布配置     错误: 文件已被删除                 │   |
| │   📁 /Users/<USER>/configs/staging.ini             │   |
| │    最后有效使用时间: 2025.06.01 12:31:25           │   |
| │                                                 │   |
| │ ❌ 备份配置       错误: 文件已被外部修改             │   |
| │   📁 /Users/<USER>/configs/backup.ini              │   |
| │    最后有效使用时间: 2025.06.01 12:31:25           │   |
| └─────────────────────────────────────────────────┘   |
+-------------------------------------------------------+
```
> 因为不存在其他路径修改配置项，`edit_process_config_unique`中所有记录都只是在新增和修改配置项时同步修改，所以其和实际配置文件中配置项的值是一致的，可用使用数据库中配置项值进行对比唯一性  

### 配置分类显示详情
1. **可用配置列表**：
   - 显示绿色状态指示器
   - 配置名称
   - 配置文件完整路径（鼠标悬停显示）
   - 最后使用时间（人性化显示：2小时前、1天前等）
   - 快速操作按钮：选择

2. **占用配置列表**：
   - 显示红色状态指示器和锁定图标
   - 配置名称
   - 配置文件完整路径（鼠标悬停显示）
   - 占用进程的PID和进程名称
   - 配置被占用的持续时间
   
3. **无效配置列表**：
   - 显示警告状态指示器
   - 配置名称
   - 配置文件完整路径（鼠标悬停显示）
   - 错误原因
   - 最后有效使用时间

### 操作按钮
- **新建配置**：不使用已有配置文件直接进入主界面创建新配置
- **导入配置**：从文件系统选择配置文件导加载到当前程序中使用
- **清除无效**：移除所有无效配置记录
- **刷新列表**：重新验证所有配置状态，在列表上列举处有效和无效状态信息

## 手动选择配置文件场景

### 必要条件（全部满足）
1. **文件格式验证**：必须是标准INI格式文件
2. **文件权限验证**：当前用户具有读写权限
3. **路径唯一性验证**：文件绝对路径不在历史记录中
4. **配置完整性验证**：包含所有必需的配置项
5. **配置格式验证**：所有配置项的key-value格式正确
7. **唯一性约束验证**：关键配置项值不与任何现有配置冲突,唯一性
8. **编码格式验证**：文件编码为UTF-8格式

### 处理流程
```mermaid
sequenceDiagram
    participant User as 用户
    participant UI as 用户界面
    participant Validator as 配置验证器
    participant FileSystem as 文件系统
    participant DB as 本地数据库
    participant ConfigParser as 配置解析器
    
    User->>UI: 点击"导入配置"按钮
    UI->>User: 显示文件选择对话框
    Note over UI: 默认过滤.ini文件
    
    User->>UI: 选择配置文件
    UI->>FileSystem: 检查文件存在性
    FileSystem-->>UI: 返回文件信息
    
    UI->>Validator: 开始验证流程
    Validator->>Validator: 文件必须是ini格式
    DB->>Validator: 检查路径唯一性
    Validator->>FileSystem: 检查文件权限
    Validator->>ConfigParser: 解析INI格式
    ConfigParser-->>Validator: 返回解析结果
    Validator->>Validator: 验证配置完整性
    Validator->>Validator: 验证配置项值格式正确性
    DB->>Validator: 检查关键配置项唯一性
    
    
    alt 所有验证通过
        Validator-->>UI: 返回验证成功
        UI->>DB: 创建配置记录
        DB-->>UI: 返回插入结果
        UI->>User: 显示导入成功提示
        UI->>UI: 进入主界面
    else 任一验证失败
        Validator-->>UI: 返回具体错误信息
        UI->>User: 显示详细错误对话框
        Note over UI: 包含修复建议
    end
```

### 错误处理场景
| 错误类型 | 检测方法 | 用户提示 | 修复建议 |
|---------|---------|---------|---------|
| 文件不存在 | 文件系统检查 | "选择的文件不存在或已被删除" | "请重新选择有效的配置文件" |
| 文件格式错误 | INI解析器 | "文件不是有效的INI格式配置文件" | "请使用标准INI格式编辑器修正文件格式" |
| 无访问权限 | 文件权限检查 | "无权限读取该配置文件" | "请检查文件权限或以管理员身份运行程序" |
| 路径已存在 | 数据库查询 | "该配置文件已在配置列表中" | "如果是有效的配置则不需要重新导入,如果是无效配置请清除无效记录之后重新导入" |
| 配置项缺失 | 配置完整性检查 | "配置文件缺少必需项：[具体配置项]" | "请在配置文件中添加缺失的配置项" |
| 配置格式错误 | 配置值验证 | "配置项[xxx]格式错误：期望[格式]，实际[值]" | "请修正配置项的值格式" |
| 唯一性配置项重复 | 唯一性检查 | "当前[xxx配置项]值[yyy]已被其他配置使用" | "请修改[xxx配置项]值" |
| 编码格式不支持 | 编码检测 | "文件编码格式不受支持" | "请将文件转换为UTF-8编码" |


## 配置文件管理规则
### 配置文件内容标准模板
```ini
[server]
whitelist = *
message_data_table_name = message_data
log_config_path = C:\Users\<USER>\.webhook_server\log_config.ini
host = 0.0.0.0
port = 8000
time_zone = Asia/Shanghai
run_time = 07:00-07:00
expire_data_days = 3
data_limit_num = 100000
api_key = 1234567890
app_name = 1

[client_info]
sendingDeviceIdentification1 = 第一个发信设备
```

### 配置文件有效性验证
1. **配置加载列表验证**：
   - 检查文件存在性
   - 验证文件哈希[本地文件是否被修改了]
   - 检查访问权限
   - 检查文件配置项完整性和格式正确性
   - 多进程文件配置项值唯一

2. **配置编辑和运行时保护**：
   - 心跳机制保护（多进程配置项互斥和无效配置项数据清除）
   - 修改后立即更新数据库哈希

### 外部修改处理
- 心跳机制或者初始化加载时检测到外部修改时：
  1. 标记配置为无效 
  3. 禁止使用该配置启动新进程
  4. 在配置选择加载界面显示警告图标和对应的错误原因
  5. 后台心跳机制检测到则清除配置项记录,配置文件记录设置状态无效

## 数据库设计
### 数据库文件位置
- **通用存储位置**: `~\.webhook_server\webhook_server.config.db`

### 配置文件使用记录表
```sql
CREATE TABLE IF NOT EXISTS  config_file_record (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL,                   -- 配置加载界面显示的名称
    file_path TEXT NOT NULL UNIQUE,       -- 文件绝对路径
    process_id INTEGER,                   -- 占用进程PID
    last_used TIMESTAMP DEFAULT CURRENT_TIMESTAMP, -- 最后使用时间[心跳机制判断占用进程是否已经无了]
    checksum TEXT NOT NULL,               -- SHA-256文件哈希
    is_active INTEGER NOT NULL DEFAULT 1 CHECK(is_active IN (0, 1)), -- 当前配置是否有效 
    fail_reason TEXT , -- 当前配置失效原因
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP, -- 创建时间
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP  -- 更新时间
);
```

### 配置文件配置项唯一约束表
```sql
CREATE TABLE IF NOT EXISTS  config_unique_constraints (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    process_unique_id INTEGER NOT NULL, -- 能够表示当前进程唯一的标识,关联 config_file_use_record.id
    config_key TEXT NOT NULL,   -- 配置键
    config_value TEXT NOT NULL, -- 配置值
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```
## 心跳处理机制
### 心跳周期与超时设置
- **心跳间隔**：15秒（适合桌面应用的响应要求）
- **超时阈值**：5分钟（进程被认为退出,对应配置占用进程信息清除）
- **清理周期**：30秒（当前轮次检测出来了下一轮次清理无效记录的间隔）

### 心跳处理详细逻辑
#### 更新存活进程心跳时间
  - 不占用进程且大于超时阈值的配置记录清除进程占用状态
  - 更新自己进程的心跳时间

#### 未被占用配置有效性检测
  - 数据库中对应的配置记录实际存在
  - 文件是ini格式
  - 实际文件可读
  - 实际文件哈希值和数据库记录一致
  - 文件编码为UTF-8
  - 将检测失效的记录标记为失效并补充失效原因
#### 清理失效配置记录
  - 清理所有失效配置记录对应的配置文件配置项唯一约束表中数据

> 数据一旦失效马上清除,最后的清除操作是将可能没有执行清除的补充操作

## 异常处理矩阵

| 异常场景 | 检测时机 | 检测方法 | 界面处理 | 后台处理 | 失效原因 |
|---------|---------|---------|---------|---------|---------|
| 配置文件被删除 | 加载配置列表/心跳时 | 文件系统检查 | 标记为无效配置 | **A1**：清理数据库配置项记录并设置配置记录无效 | "该配置文件已被删除？" |
| 配置被外部修改 | 加载配置列表/心跳时 | SHA-256哈希对比 | 标记为无效配置 | 同A1操作 | "配置已被外部修改，配置被视为无效" |
| 无文件访问权限 | 加载配置列表/心跳时 | 文件读写权限测试 | 显示权限错误 | 同A1操作 | "配置文件权限不足，请检查文件权限" |
| 配置文件配置项缺失 | 加载配置列表 | 配置项校验 | 标记为无效配置 | 同A1操作 | "配置文件配置项缺失，其中[xxx]是缺失的" |
| 配置文件配置项格式错误 | 加载配置列表 | 配置项值格式校验 | 标记为无效配置 | 同A1操作 | "配置文件配置项值格式错误：其中[xxx]的值格式错误" |
| 配置文件配置项值不唯一 | 加载配置列表 | 配置项值唯一性校验 | 标记为无效配置 | 同A1操作 | "配置文件配置项值不唯一：其中[xxx]的值不能重复" |
| 应用异常崩溃 | 心跳超时 | 进程存活检测 | 自动释放占用状态 | 清除对应数据库中配置记录中进程占用状态 | --- |
| 所有配置被占用 | 加载配置列表 | 占用状态统计 | 只能进入主界面新建配置 | ---- | --- |

## 安全与可靠性增强

1. **配置文件可读**
2. **数据库安全**
  - 定期进行数据库完整性检测
  - 所有数据库操作都使用事务保证一致性
3. **进程安全**
  - 占用配置对应进行存活校验
4. **操作安全**
  - 重要操作需二次确认

> 不需要操作日志的保存，其删除操作也只是数据库记录的删除，不涉及实际文件的删除
