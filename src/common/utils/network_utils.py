"""网络工具模块。

此模块提供了网络相关的工具函数，包括：
- IP地址验证和白名单检查
- 端口占用检测
- 本地网络配置获取
- 网络地址解析
"""

import logging
import socket
from ipaddress import ip_address, ip_network
from typing import List

import psutil

from common.utils import common_utils, logging_utils

logger = logging.getLogger(__name__)


def is_ip_in_whitelist(client_ip: str, whitelist: list[str]) -> bool:
    """判断IP是否在白名单中
    
    Args:
        client_ip: 客户端IP地址
        whitelist: IP白名单列表
        
    Returns:
        bool: 是否在白名单中
    """
    logging_utils.logger_print(f"checking if ip {client_ip} is in whitelist: {whitelist}", custom_logger=logger)
    try:
        parsed_client_ip = ip_address(client_ip)
        logging_utils.logger_print(f"parsed client ip: {parsed_client_ip}, version: {parsed_client_ip.version}", custom_logger=logger)

        for i, whitelist_ip in enumerate(whitelist):
            logging_utils.logger_print(f"checking against whitelist entry {i+1}: {whitelist_ip}", custom_logger=logger)
            network = ip_network(whitelist_ip, strict=False)
            logging_utils.logger_print(f"parsed network: {network}, version: {network.version}", custom_logger=logger)

            if parsed_client_ip.version == network.version and parsed_client_ip in network:
                logging_utils.logger_print(f"ip {client_ip} matches whitelist entry {whitelist_ip}", custom_logger=logger)
                return True

        logging_utils.logger_print(f"ip {client_ip} does not match any whitelist entries", custom_logger=logger)
        return False
    except ValueError:
        logging_utils.logger_print(f"invalid ip address: {client_ip}!", custom_logger=logger, use_exception=True)
        return False


def is_local_host(host: str) -> bool:
    """判断host是否指代本机IP
    
    Args:
        host: 主机地址
        
    Returns:
        bool: 是否为本机IP
        
    Raises:
        ValueError: 主机地址无效
    """
    if not host or not isinstance(host, str):
        raise ValueError("host must be a non-empty string!")
    host = host.lower()
    if host in ["localhost", "127.0.0.1", "0.0.0.0", "::1", "0:0:0:0:0:0:0:1"]:
        return True
    try:
        host_ip = ip_address(host)
        return host_ip.is_loopback
    except ValueError:
        logging_utils.logger_print(f"invalid host: {host}", custom_logger=logger, use_exception=True)
        raise


def is_port_occupied(port: int, ip: str = "127.0.0.1") -> bool:
    """检查指定IP和端口是否被占用
    
    该函数用于确定特定IP地址和端口组合是否已被其他服务占用，
    通常用于在启动服务前检查端口可用性。
    
    Args:
        port: 端口号
        ip: 要检查的IP地址 (默认127.0.0.1)
        
    Returns:
        bool: True表示占用，False表示空闲
    """
    for conn in psutil.net_connections(kind='inet'):
        # 检查TCP监听连接
        if conn.status == 'LISTEN' and conn.laddr:
            addr_ip, addr_port = conn.laddr
            # 检查端口和IP是否匹配（包括0.0.0.0的特殊情况） 0.0.0.0 的特殊检查必须包含在内
            if addr_port == port and (ip == '0.0.0.0' or addr_ip == '0.0.0.0' or addr_ip == ip):
                return True
    return False


def get_local_lan_ip(fallback: str = "127.0.0.1") -> str:
    """获取本机在局域网中的 IPv4 地址。

    原理：向一个外部地址（不一定可达）创建 UDP "连接"，
    OS 会自动选择一条出网的本地网卡和 IP，随后我们从 socket 获取到本地地址。

    Args:
        fallback: 如果获取失败，返回的默认 IP（回环地址）。
        
    Returns:
        str: 本机局域网 IPv4 地址字符串。
    """
    try:
        with socket.socket(socket.AF_INET, socket.SOCK_DGRAM) as s:
            # 这里的 IP 和端口不需要真的可达，只要是合法的 IPv4 格式即可
            s.connect(("*******", 80))
            local_ip = s.getsockname()[0]
    except Exception:  # noqa
        local_ip = fallback
    return local_ip


def get_whitelist(whitelist_config_value: str) -> List[str]:
    """将IP白名单对应的字符串转换为IP白名单列表:其中*表示允许所有IP访问
    
    Args:
        whitelist_config_value: 白名单配置值字符串
        
    Returns:
        List[str]: IP白名单列表
        
    Raises:
        ValueError: 白名单配置无效
    """
    res_list: List[str] = []
    allowed_all = False
    for whitelist_ip in whitelist_config_value.split(","):
        # 校验字符串必须是IP或者网段
        new_whitelist_ip = common_utils.normalize_str(whitelist_ip)
        if new_whitelist_ip is None:
            continue
        # 不限制白名单
        if new_whitelist_ip == "*":
            allowed_all = True
            res_list.clear()
            res_list.append("*")
            continue
        # 校验IP或者网段是否合法
        try:
            ip_network(new_whitelist_ip, strict=False)
        except ValueError:
            raise ValueError(
                f"server properties [server]-[whitelist] value must be a valid IP or IP network,current value is {whitelist_ip}")
        if not allowed_all:
            res_list.append(new_whitelist_ip)
    if not res_list:
        raise ValueError("server properties [server]-[whitelist] value can not empty!")
    return res_list


def check_lan_host(lan_host: str):
    """检查局域网主机地址是否有效
    
    Args:
        lan_host: 局域网主机地址
        
    Raises:
        ValueError: 主机地址无效或不是私有IP
    """
    # 必须是局域网IP
    try:
        host_ip = ip_address(lan_host)
    except ValueError:
        raise ValueError("server properties [server]-[host] value must be a valid IP address!")
    if not host_ip.is_private:
        raise ValueError("server properties [server]-[host] value must be a private IP address!")
