"""
多进程文件锁测试 - 需要单独运行
这些测试会启动多个进程，因此需要单独运行而不是作为常规pytest套件的一部分
"""

import os
import sys
import time
import multiprocessing
import tempfile
import pytest

# 确保能够导入 src 目录下的模块
src_path = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', 'src'))
if src_path not in sys.path:
    sys.path.append(src_path)

from common.utils.file_lock import FileLock, is_file_locked

# 全局变量用于进程间通信
LOCK_ACQUIRED = "LOCK_ACQUIRED"
LOCK_RELEASED = "LOCK_RELEASED"
LOCK_FAILED = "LOCK_FAILED"
LOCK_TIMEOUT = "LOCK_TIMEOUT"

def acquire_lock_process(file_path, blocking, timeout, queue, hold_time=0):
    """在单独进程中获取锁"""
    try:
        lock = FileLock(file_path, blocking=blocking, timeout=timeout)
        queue.put(LOCK_ACQUIRED)
        
        # 持有锁一段时间
        time.sleep(hold_time)
        
        # 释放锁
        lock.release()
        queue.put(LOCK_RELEASED)
        return True
    except TimeoutError:
        queue.put(LOCK_TIMEOUT)
        return False
    except Exception as e:
        queue.put(f"{LOCK_FAILED}: {str(e)}")
        return False

@pytest.fixture
def temp_lockfile():
    """创建临时文件用于测试"""
    fd, path = tempfile.mkstemp()
    os.close(fd)
    yield path
    # 清理
    try:
        os.remove(path)
    except (FileNotFoundError, PermissionError):
        pass
    try:
        os.remove(path + '.lock')
    except (FileNotFoundError, PermissionError):
        pass

def test_two_processes_non_blocking(temp_lockfile):
    """测试两个进程非阻塞模式下的锁竞争"""
    # 创建队列用于进程间通信
    queue = multiprocessing.Queue()
    
    # 第一个进程获取锁
    p1 = multiprocessing.Process(
        target=acquire_lock_process,
        args=(temp_lockfile, False, 1, queue, 2)  # 持有锁2秒
    )
    p1.start()
    
    # 等待第一个进程获取锁
    result1 = queue.get(timeout=5)
    assert result1 == LOCK_ACQUIRED, "第一个进程应该成功获取锁"
    
    # 第二个进程尝试获取锁（非阻塞模式）
    p2 = multiprocessing.Process(
        target=acquire_lock_process,
        args=(temp_lockfile, False, 1, queue, 0)
    )
    p2.start()
    
    # 第二个进程应该失败
    result2 = queue.get(timeout=5)
    assert LOCK_FAILED in result2, "第二个进程应该无法获取锁"
    
    # 等待第一个进程释放锁
    result3 = queue.get(timeout=5)
    assert result3 == LOCK_RELEASED, "第一个进程应该成功释放锁"
    
    # 清理
    p1.join()
    p2.join()

def test_two_processes_blocking_with_timeout(temp_lockfile):
    """测试两个进程阻塞模式下的锁竞争和超时"""
    # 创建队列用于进程间通信
    queue = multiprocessing.Queue()
    
    # 第一个进程获取锁并持有3秒
    p1 = multiprocessing.Process(
        target=acquire_lock_process,
        args=(temp_lockfile, False, 1, queue, 3)
    )
    p1.start()
    
    # 等待第一个进程获取锁
    result1 = queue.get(timeout=5)
    assert result1 == LOCK_ACQUIRED, "第一个进程应该成功获取锁"
    
    # 第二个进程尝试获取锁（阻塞模式，2秒超时）
    p2 = multiprocessing.Process(
        target=acquire_lock_process,
        args=(temp_lockfile, True, 2, queue, 0)
    )
    p2.start()
    
    # 第二个进程应该超时
    result2 = queue.get(timeout=5)
    assert result2 == LOCK_TIMEOUT, "第二个进程应该因超时而失败"
    
    # 等待第一个进程释放锁
    result3 = queue.get(timeout=5)
    assert result3 == LOCK_RELEASED, "第一个进程应该成功释放锁"
    
    # 清理
    p1.join()
    p2.join()

def test_process_termination_releases_lock(temp_lockfile):
    """测试进程终止时是否正确释放锁"""
    # 创建队列用于进程间通信
    queue = multiprocessing.Queue()
    
    # 进程获取锁
    p = multiprocessing.Process(
        target=acquire_lock_process,
        args=(temp_lockfile, False, 1, queue, 10)  # 持有锁10秒
    )
    p.start()
    
    # 等待进程获取锁
    result = queue.get(timeout=5)
    assert result == LOCK_ACQUIRED, "进程应该成功获取锁"
    
    # 确认文件已被锁定
    time.sleep(0.5)  # 给一点时间确保锁定状态已稳定
    assert is_file_locked(temp_lockfile), "文件应该被锁定"
    
    # 终止进程
    p.terminate()
    p.join(timeout=2)
    
    # 等待一段时间确保系统有机会清理资源
    time.sleep(2)
    
    # 检查锁是否被释放
    assert not is_file_locked(temp_lockfile), "进程终止后锁应该被释放"
    
    # 尝试获取锁
    lock = FileLock(temp_lockfile)
    assert lock.lock_acquired, "应该能够获取之前被终止进程持有的锁"
    lock.release()

def test_multiple_processes_sequential_locking(temp_lockfile):
    """测试多个进程顺序获取锁"""
    # 创建队列用于进程间通信
    queue = multiprocessing.Queue()
    processes = []
    
    # 启动5个进程，每个进程获取锁后立即释放
    for i in range(5):
        p = multiprocessing.Process(
            target=acquire_lock_process,
            args=(temp_lockfile, True, 10, queue, 0.5)  # 每个进程持有锁0.5秒
        )
        processes.append(p)
        p.start()
    
    # 收集结果
    results = []
    for _ in range(10):  # 每个进程会产生两个消息：获取锁和释放锁
        try:
            results.append(queue.get(timeout=15))
        except Exception:
            break
    
    # 验证结果
    acquired_count = results.count(LOCK_ACQUIRED)
    released_count = results.count(LOCK_RELEASED)
    
    assert acquired_count == 5, f"应该有5个进程获取锁，实际有{acquired_count}个"
    assert released_count == 5, f"应该有5个进程释放锁，实际有{released_count}个"
    
    # 清理
    for p in processes:
        p.join(timeout=1)

if __name__ == "__main__":
    # 直接运行此文件时
    i=1
