import ttkbootstrap as tb
from ttkbootstrap import Style, Button

# 创建窗口
app = tb.Window(themename="flatly")

# 设置字体（可尝试换 Arial 或系统默认字体）
cur_font = ("华文楷体", 16)

# 样式配置 - 一定要在按钮创建前
style = Style()
style.configure("Outline.Toolbutton", font=cur_font)
style.configure("Outline.TButton", font=cur_font)

# 创建按钮
btn = Button(app, text="测试按钮", bootstyle="outline")  # 实际使用 Outline.Toolbutton 样式
btn.pack(pady=20)
same_style="outline-toolbutton"
Style().configure('Outline.Toolbutton', font=cur_font)
tkb1=Button(app, text="新增配置",bootstyle=same_style)
tkb1.pack(pady=20)
# 输出实际样式名
print("按钮使用样式:", btn.cget("style"))

app.mainloop()
