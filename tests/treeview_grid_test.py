import tkinter
from tkinter import ttk

from config import gui_constants

from common.models import gui_widgets
from common.utils import gui_utils


def error_test(parent_frame:ttk.Frame,need_tree=True):
    """
    不需要tree但定义包含#0列
    需要tree但定义不包含#0列
    :param need_tree: 是否需要tree --- 在第一个节点使用图标照片
    :param parent_frame:
    """
    if need_tree:
        first_col_id='icon'
    else:
        first_col_id='#0'
    cols = [
        {gui_widgets.TreeviewWithFixedGrid.COL_ID_KEY: first_col_id, gui_widgets.TreeviewWithFixedGrid.DISPLAY_TEXT_KEY: '图标', gui_widgets.TreeviewWithFixedGrid.WIDTH_KEY: 120},
        {gui_widgets.TreeviewWithFixedGrid.COL_ID_KEY: 'last_name', gui_widgets.TreeviewWithFixedGrid.DISPLAY_TEXT_KEY: 'Last Name', gui_widgets.TreeviewWithFixedGrid.WIDTH_KEY: 120},
        {gui_widgets.TreeviewWithFixedGrid.COL_ID_KEY: 'email', gui_widgets.TreeviewWithFixedGrid.DISPLAY_TEXT_KEY: 'Email Address', gui_widgets.TreeviewWithFixedGrid.WIDTH_KEY: 250}
    ]

    tv = gui_widgets.TreeviewWithFixedGrid(parent=parent_frame, font_family='Calibri', font_size=12, row_height=25, columns= cols, need_tree=need_tree)
    tv.pack(side='left', fill='both', expand=True)

    # 添加滚动条
    scrollbar = ttk.Scrollbar(parent_frame, orient="vertical", command=tv.yview)
    scrollbar.pack(side='right', fill='y')
    tv.configure(yscrollcommand=scrollbar.set)

def no_tree_with_0_test(parent_frame:ttk.Frame):
    """不需要tree但定义包含#0列的测试"""
    error_test(parent_frame, need_tree=False)

def tree_with_no0_test(parent_frame:ttk.Frame):
    """需要tree但定义不包含#0列的测试"""
    error_test(parent_frame, need_tree=True)

def ok_treeview_show_test(parent_frame:ttk.Frame,need_tree=True):
    if need_tree:
        first_col_id='#0'
    else:
        first_col_id='icon'
    # 列定义: (column_id, 显示名称, 宽度)
    cols = [
        {gui_widgets.TreeviewWithFixedGrid.COL_ID_KEY: first_col_id, gui_widgets.TreeviewWithFixedGrid.DISPLAY_TEXT_KEY: '图标', gui_widgets.TreeviewWithFixedGrid.WIDTH_KEY: 120},
        {gui_widgets.TreeviewWithFixedGrid.COL_ID_KEY: 'last_name', gui_widgets.TreeviewWithFixedGrid.DISPLAY_TEXT_KEY: 'Last Name', gui_widgets.TreeviewWithFixedGrid.WIDTH_KEY: 120},
        {gui_widgets.TreeviewWithFixedGrid.COL_ID_KEY: 'email', gui_widgets.TreeviewWithFixedGrid.DISPLAY_TEXT_KEY: 'Email Address', gui_widgets.TreeviewWithFixedGrid.WIDTH_KEY: 150}
    ]

    tv = gui_widgets.TreeviewWithFixedGrid(parent=parent_frame, font_family='Calibri', font_size=12, row_height=25, columns= cols, need_tree=need_tree)
    tv.images.append(gui_utils.create_red_x_image(tv.row_height))
    tv.images.append(gui_utils.create_color_circle_image(color=gui_constants.RED_COLOR, row_height=tv.row_height))
    tv.pack(side='left', fill='both', expand=True)

    # 添加滚动条
    scrollbar = ttk.Scrollbar(parent_frame, orient="vertical", command=tv.yview)
    scrollbar.pack(side='right', fill='y')
    tv.configure(yscrollcommand=scrollbar.set)

    # 自定义行处理函数示例
    def custom_row_processor(treeview, item_id, row_data, index, tag): # noqa
        # 设置额外的标签
        treeview.item(item_id, tags=(tag, 'custom_tag'))
        if treeview.need_tree is not None and treeview.need_tree:
            treeview.item(item_id, image=treeview.images[0])

    # 初始无数据，但仍会显示表头下方的横线
    # 添加数据按钮
    def add_data():
        data = [
            {first_col_id: 'John可用', 'last_name': 'Doe', 'email': '<EMAIL>'},
            {first_col_id: 'Jane', 'last_name': 'Smith', 'email': '<EMAIL>'},
            {first_col_id: 'Alice', 'last_name': 'Wang', 'email': '<EMAIL>'},
        ]
        tv.add_rows(data, row_processor=custom_row_processor)

    # 添加更多数据按钮
    def add_more_data():
        new_data = [
            {first_col_id: 'Robert', 'last_name': 'Johnson', 'email': '<EMAIL>'},
            {first_col_id: 'Emily', 'last_name': 'Davis', 'email': '<EMAIL>'},
            {first_col_id: 'Michael', 'last_name': 'Brown', 'email':'<EMAIL>'},
            {first_col_id: 'Sarah', 'last_name': 'Wilson', 'email':'<EMAIL>'},
        ]
        tv.add_rows(new_data, row_processor=custom_row_processor)

    btn_frame = ttk.Frame(root)
    btn_frame.pack(pady=10)

    add_btn = ttk.Button(btn_frame, text="Add Initial Data", command=add_data)
    add_btn.pack(side='left', padx=5)

    more_btn = ttk.Button(btn_frame, text="Add More Data", command=add_more_data)
    more_btn.pack(side='left', padx=5)

    clear_btn = ttk.Button(btn_frame, text="Clear Data", command=tv.clear_all_data)
    clear_btn.pack(side='left', padx=5)


# 示例用法
if __name__ == '__main__':
    root = tkinter.Tk()
    root.title('Fixed Grid Treeview Demo')
    root.geometry('650x400')
    frame = ttk.Frame(root)
    frame.pack(fill='both', expand=True, padx=10, pady=10)
    # no_tree_with_0_test(frame)
    ok_treeview_show_test(frame,need_tree=True)

    root.mainloop()

