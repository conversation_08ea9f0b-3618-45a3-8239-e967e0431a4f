import concurrent
import random
import time
from typing import List

import requests

from config import constants
from models import server_data, server_properties


def read_by_client_id_request(ip_param:str,port_param:str,token: str, client_id: str, size: int) -> List[str]:
    print(f"正在获取{client_id}的未读消息...")
    headers = {'Authorization': f'Bearer {token}'}
    params = {'client_key': client_id, 'size': size}
    get_unread_api_url=f"http://{ip_param}:{port_param}/webhook/unread" # noqa
    response = requests.get(get_unread_api_url, headers=headers, params=params)
    if response.status_code != 200:
        raise server_data.TokenExpiredError()
    data = response.json()
    return [msg['message'] for msg in data.get('messages', [])]


def get_token(ip_param:str,port_param:str,api_key_param: str) -> str:
    headers = {'Authorization': f'Bearer {api_key_param}'}
    token_url = f"http://{ip_param}:{port_param}/webhook/token" # noqa
    response = requests.get(token_url, headers=headers)
    if response.status_code != 200:
        raise Exception('获取token失败')
    token = response.json()['token']
    if not token:
        raise Exception('获取token失败')
    return token


# 从api_url获取所属client_id[允许为空]未读1条消息,如果一直没有消息,则每隔3秒请求一次直到有消息为止,然后返回
# api_url是get请求,其中header传递Authorization: Bearer token,如果返回不是200,则使用token_url获取token,再次请求api_url
# token_url是get请求,其中只需要传递参数api_key,返回token
def read_by_client_id(ip_param:str,port_param:str,api_key_param: str, client_id: str = None,size:int=None) -> list[str]:
    # 获取token
    token = get_token(ip_param, port_param, api_key_param)
    if size is None:
        size = 100
    try:
        unread_msgs = read_by_client_id_request(ip_param, port_param,token, client_id, size)
    except server_data.TokenExpiredError:
        print("token过期,重新获取token...")
        # 重新获取token
        token = get_token(ip_param, port_param, api_key_param)
        unread_msgs = read_by_client_id_request(ip_param, port_param,token, client_id, size)
    if unread_msgs:
        print(f"获取到{client_id}的未读消息: {unread_msgs}")
        return unread_msgs

    return []

def must_read_msg(ip_param:str,port_param:str,api_key_param: str, client_id: str = None,size:int=None) -> list[str]:
    # 获取token
    token = get_token(ip_param, port_param, api_key_param)
    if size is None:
        size = 100
    while True:
        unread_msgs=read_by_client_id(ip_param, port_param, api_key_param, client_id, size)
        if unread_msgs:
            return unread_msgs

        print("当前没有未读消息,等待3秒后再次请求...")
        time.sleep(3)
def read_one_msg(ip_param:str,port_param:str,api_key_param: str, client_keys_param: list[str]):
    print(f"client_key_set: {client_keys_param}")
    for client_key in client_keys_param:
        unread_msg = read_by_client_id(ip_param, port_param, api_key_param, client_key,1)
        print(f"client_key: {client_key}, unread_msg: {unread_msg}")


def read_all_msg(ip_param:str,port_param:str,api_key_param: str):
    while True:
        print(f"messages:{read_by_client_id(ip_param, port_param, api_key_param, None)}")

# 多线程随机读取所有client的未读消息
def read_with_multi_thread(ip_param:str,port_param:str,api_key_param: str, client_keys_param: list[str]):
    client_keys_param.append(None)
    def thread_read_msg():
        for _ in range(10):
            cur_client_key = random.choice(client_keys_param)
            cur_size = random.randint(5, 31)
            unread_msg = read_by_client_id(ip_param, port_param, api_key_param, cur_client_key,cur_size)
            if unread_msg:
                print(f"client_key: {cur_client_key}, unread_msg: {unread_msg}")

    with concurrent.futures.ThreadPoolExecutor(max_workers=50) as executor:
        # 重复执行 100 次
        futures = [executor.submit(thread_read_msg) for _ in range(100)]
    for future in futures:
        try:
            future.result()  # 可以在这里加异常捕获以防请求出错
        except Exception as e:
            print(f"Request error: {e}")

    time.sleep(3)




if __name__ == '__main__':
    config_path="D:\\Git\\python-samples-hub\\tests\\other_conf\\ok\\server_config_3.ini"
    ip='************'
    port='7779'
    server_properties =  server_properties.ServerProperties(config_path, constants.DEFAULT_SCHEDULER_CONFIG)
    api_key = server_properties.server_config['api_key']
    # api_key = 'x'
    client_keys = list(server_properties.client_info_properties.keys())
    # read_one_msg(ip,port,api_key, client_keys)
    read_with_multi_thread(ip,port,api_key, client_keys)
    # read_all_msg(api_key)
