# SQLite具体执行sql语句打印
import logging
import sqlite3

from common.utils import logging_utils

logger = logging.getLogger(__name__)

class LoggingCursor(sqlite3.Cursor):
    def execute(self, sql, parameters=()):
        def format_value(v):
            # 处理列表/元组参数
            if isinstance(v, (list, tuple)):
                return "(%s)" % ", ".join(format_value(x) for x in v)
            # 字符串需要加引号
            elif isinstance(v, str):
                return f"'{v}'"
            # 其他类型直接转字符串
            else:
                return str(v)

        if parameters:
            try:
                # 将参数拆解为元组（适配多参数情况）
                formatted_params = tuple(format_value(p) for p in parameters)
                # 替换占位符（需确保占位符数量与参数数量匹配）
                formatted_sql = sql.replace("?", "%s") % formatted_params
            except Exception as e:
                logging_utils.logger_print("sql parameter formatting failed!", custom_logger=logger, use_exception=True, exception=e)
                formatted_sql = f"{sql} /* parameter formatting failed: {e} */"
        else:
            formatted_sql = sql

        logging_utils.logger_print(f"executing SQL: {formatted_sql}", custom_logger=logger, log_level=logging.WARNING)
        return super().execute(sql, parameters)


# 自定义连接工厂
class LoggingConnection(sqlite3.Connection):
    def cursor(self, *args, **kwargs):
        return LoggingCursor(self)
