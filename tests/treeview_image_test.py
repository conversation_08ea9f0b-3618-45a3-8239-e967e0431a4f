import tkinter
from tkinter import ttk

from common.utils import gui_utils


class TreeWithRedIcon(ttk.Treeview):
    def __init__(self, master=None, **kwargs):
        super().__init__(master, **kwargs)
        # create_red_x_image() create_color_circle_image(color=gui_constants.green_color)
        self.red_circle_img = gui_utils.create_red_x_image()


    def insert_with_tag_icon(self, parent, index, iid=None, **kw):
        tags = kw.get("tags", [])
        values = list(kw.get("values", []))
        abc_text = values[0] if len(values) > 0 else ""

        # 如果有 tag=xxx，显示图标
        if "xxx" in tags:
            kw["image"] = self.red_circle_img
        else:
            kw["image"] = ""

        kw["text"] = abc_text  # tree列显示
        # 除首列外的 values
        kw["values"] = values[1:]
        return self.insert(parent, index, iid=iid, **kw)

# ====== 主窗口 ======
if __name__ == '__main__':
    root = tkinter.Tk()
    root.title("Treeview abc列显示图标")
    root.geometry("500x300")

    # ====== Treeview 设置 ======
    # columns 不包含 tree column，即 "abc" 是 tree column（#0）
    tree = TreeWithRedIcon(root, columns=("other",), show="tree headings")
    tree.pack(fill="both", expand=True)

    # 设置 tree列（即abc列）
    tree.heading("#0", text="ABC")         # tree列的表头
    tree.column("#0", width=150)

    # 设置其他列
    tree.heading("other", text="Other")
    tree.column("other", width=300)

    # ====== 插入数据 ======
    tree.insert_with_tag_icon("", "end", values=["abc内容1", "其他1"], tags=["xxx"])  # 显示红圈
    tree.insert_with_tag_icon("", "end", values=["abc内容2", "其他2"])              # 无图标
    tree.insert_with_tag_icon("", "end", values=["abc内容3", "其他3"], tags=["xxx"])  # 显示红圈

    root.mainloop()
