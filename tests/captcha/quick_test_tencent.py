"""快速测试腾讯云验证码产品页的滑块验证码。

专门用于测试 https://cloud.tencent.com/product/captcha 页面的"立即体验"功能。
"""

import asyncio
import logging
import sys
from pathlib import Path

# 添加项目根目录到 Python 路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from src.common.utils import logging_utils
from src.captcha.recognizer.t_sec.tencent_slider_solver import TencentSliderSolver

logger = logging.getLogger(__name__)


async def quick_test():
    """快速测试腾讯云验证码产品页。"""
    print("🚀 腾讯云验证码快速测试")
    print("=" * 50)
    print("测试页面: https://cloud.tencent.com/product/captcha")
    print("目标: 点击'立即体验'按钮并解决滑块验证码")
    print("=" * 50)
    
    # 创建解决器
    solver = TencentSliderSolver(
        ddddocr_base_url="http://10.168.1.201:7777",
        headless=False,  # 显示浏览器以便观察
        browser_type="chromium"
    )
    
    async with solver:
        try:
            # 步骤1: 导航到页面
            print("\n📍 步骤1: 导航到腾讯云验证码产品页...")
            await solver.navigate_to_page("https://cloud.tencent.com/product/captcha")
            print("✅ 页面加载完成")
            
            # 等待页面完全加载
            await asyncio.sleep(5)
            
            # 步骤2: 查找并点击"立即体验"按钮
            print("\n🔍 步骤2: 查找'立即体验'按钮...")
            
            # 尝试多种选择器
            selectors_to_try = [
                '#captcha_click',
                'a.tpm-btn',
                '.tpm-btn',
                'a[id*="captcha"]',
                'a:has-text("立即体验")',
                'button:has-text("立即体验")',
                '.btn:has-text("体验")',
                'a[href*="captcha"]'
            ]
            
            experience_button = None
            used_selector = None
            
            for selector in selectors_to_try:
                try:
                    experience_button = await solver.page.query_selector(selector)
                    if experience_button:
                        # 检查元素是否可见
                        is_visible = await experience_button.is_visible()
                        if is_visible:
                            used_selector = selector
                            print(f"✅ 找到体验按钮: {selector}")
                            break
                        else:
                            print(f"⚠️  找到按钮但不可见: {selector}")
                except Exception as e:
                    print(f"❌ 选择器失败 {selector}: {e}")
                    continue
            
            if not experience_button:
                print("❌ 未找到'立即体验'按钮")
                print("🔍 尝试查找页面中所有可能的按钮...")
                
                # 列出页面中所有按钮和链接
                all_buttons = await solver.page.query_selector_all('button, a, input[type="button"], input[type="submit"]')
                print(f"页面中共找到 {len(all_buttons)} 个可点击元素:")
                
                for i, btn in enumerate(all_buttons[:10]):  # 只显示前10个
                    try:
                        text = await btn.inner_text()
                        tag = await btn.evaluate('el => el.tagName')
                        id_attr = await btn.get_attribute('id')
                        class_attr = await btn.get_attribute('class')
                        
                        print(f"  {i+1}. {tag} - 文本: '{text}' - ID: {id_attr} - Class: {class_attr}")
                        
                        # 如果文本包含相关关键词，尝试点击
                        if text and any(keyword in text for keyword in ['体验', '验证', '测试', '开始']):
                            print(f"    🎯 尝试点击这个元素...")
                            experience_button = btn
                            used_selector = f"元素{i+1}"
                            break
                    except:
                        continue
            
            if not experience_button:
                print("❌ 仍未找到合适的按钮，测试终止")
                return False
            
            # 步骤3: 点击按钮
            print(f"\n👆 步骤3: 点击按钮 ({used_selector})...")
            try:
                await experience_button.click()
                print("✅ 按钮点击成功")
            except Exception as e:
                print(f"❌ 点击失败: {e}")
                return False
            
            # 步骤4: 等待验证码出现
            print("\n⏳ 步骤4: 等待验证码出现...")
            print("   (根据网络情况，可能需要等待3-10秒...)")
            captcha_appeared = await solver.wait_for_captcha(timeout=30000)  # 增加到30秒

            if not captcha_appeared:
                print("⚠️  验证码未立即出现，尝试其他操作...")

                # 尝试滚动页面
                await solver.page.evaluate("window.scrollTo(0, document.body.scrollHeight)")
                await asyncio.sleep(2)

                # 检查是否有 iframe
                print("🔍 检查是否有验证码 iframe...")
                iframes = await solver.page.query_selector_all('iframe')
                for i, iframe in enumerate(iframes):
                    try:
                        src = await iframe.get_attribute('src')
                        if src and 'captcha' in src.lower():
                            print(f"找到验证码 iframe: {src}")
                            # 切换到 iframe
                            frame = await iframe.content_frame()
                            if frame:
                                print("切换到 iframe 内部...")
                                # 在 iframe 内查找验证码
                                tc_opera = await frame.query_selector('.tc-opera')
                                if tc_opera:
                                    print("✅ 在 iframe 中找到验证码！")
                                    captcha_appeared = True
                                    # 更新 solver 的 page 为 frame
                                    solver.page = frame
                                    break
                    except:
                        continue

                if not captcha_appeared:
                    # 尝试查找其他验证码选择器
                    print("🔍 尝试查找其他验证码元素...")
                    alternative_captcha_selectors = [
                        '.captcha',
                        '[class*="captcha"]',
                        '[id*="captcha"]',
                        '.verify',
                        '.verification',
                        '[class*="verify"]'
                    ]

                    for selector in alternative_captcha_selectors:
                        try:
                            elements = await solver.page.query_selector_all(selector)
                            if elements:
                                print(f"找到可能的验证码元素: {selector} ({len(elements)}个)")
                                captcha_appeared = True
                                break
                        except:
                            continue

                if not captcha_appeared:
                    # 再次检查标准的腾讯验证码
                    captcha_appeared = await solver.wait_for_captcha(timeout=10000)

                if not captcha_appeared:
                    print("❌ 验证码仍未出现")

                    # 截图以便调试
                    try:
                        await solver.page.screenshot(path="no_captcha_debug.png")
                        print("📸 调试截图已保存: no_captcha_debug.png")
                    except:
                        pass

                    # 提供手动操作选项
                    print("\n🤔 可能的原因:")
                    print("1. 页面结构发生了变化")
                    print("2. 需要额外的用户交互才能触发验证码")
                    print("3. 验证码在 iframe 或弹窗中")
                    print("4. 网站检测到自动化工具")

                    print("\n💡 建议:")
                    print("1. 运行 debug_tencent_page.py 分析页面结构")
                    print("2. 手动在浏览器中触发验证码")
                    print("3. 检查网络连接和页面加载状态")

                    return False
            
            print("✅ 检测到腾讯滑块验证码！")
            
            # 步骤5: 解决验证码
            print("\n🧩 步骤5: 开始解决滑块验证码...")
            
            # 先截图记录初始状态
            try:
                await solver.page.screenshot(path="captcha_initial.png")
                print("📸 初始状态截图: captcha_initial.png")
            except:
                pass
            
            success = await solver.solve_captcha(max_retries=3)
            
            if success:
                print("🎉 验证码解决成功！")
                
                # 等待验证结果
                await asyncio.sleep(3)
                
                # 截图保存成功状态
                try:
                    await solver.page.screenshot(path="captcha_success.png")
                    print("📸 成功截图: captcha_success.png")
                except:
                    pass
                
                print("✅ 测试完成 - 成功！")
                return True
            else:
                print("❌ 验证码解决失败")
                
                # 截图保存失败状态
                try:
                    await solver.page.screenshot(path="captcha_failed.png")
                    print("📸 失败截图: captcha_failed.png")
                except:
                    pass
                
                print("❌ 测试完成 - 失败")
                return False
                
        except Exception as e:
            print(f"\n💥 测试过程中出现错误: {e}")
            logging_utils.logger_print(
                msg="quick test failed",
                custom_logger=logger,
                use_exception=True,
                exception=e
            )
            
            # 错误截图
            try:
                await solver.page.screenshot(path="error_debug.png")
                print("📸 错误截图: error_debug.png")
            except:
                pass
            
            return False


async def main():
    """主函数。"""
    # 配置日志
    logging.basicConfig(level=logging.INFO)
    
    try:
        print("准备开始测试...")
        print("请确保:")
        print("1. ddddocr 服务正在运行 (http://10.168.1.201:7777)")
        print("2. 网络连接正常")
        print("3. 已安装 playwright 浏览器: playwright install chromium")
        print()
        
        input("按回车键开始测试...")
        
        # 运行测试
        success = await quick_test()
        
        print("\n" + "=" * 50)
        if success:
            print("🎉 测试结果: 成功！")
            print("腾讯滑块验证码已成功解决")
        else:
            print("❌ 测试结果: 失败")
            print("请检查:")
            print("- ddddocr 服务是否正常运行")
            print("- 网络连接是否正常")
            print("- 页面结构是否发生变化")
        print("=" * 50)
        
    except KeyboardInterrupt:
        print("\n⏹️  用户中断测试")
    except Exception as e:
        print(f"\n💥 程序出错: {e}")
    
    print("\n测试结束")


if __name__ == "__main__":
    asyncio.run(main())
