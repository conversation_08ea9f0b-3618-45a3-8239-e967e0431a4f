# NexusRecv - 多类别设备数据接收存储桌面应用

[![Python Version](https://img.shields.io/badge/python-3.11+-blue.svg)](https://python.org)
[![License](https://img.shields.io/badge/license-Apache%202.0-green.svg)](LICENSE)
[![Platform](https://img.shields.io/badge/platform-Windows%20%7C%20Linux%20%7C%20macOS-lightgrey.svg)](README.md)
[![GUI Framework](https://img.shields.io/badge/GUI-ttkbootstrap-orange.svg)](https://github.com/israel-dryer/ttkbootstrap)
[![Backend](https://img.shields.io/badge/backend-FastAPI-009688.svg)](https://fastapi.tiangolo.com/)

> 🚀 基于FastAPI和ttkbootstrap的现代化桌面GUI应用程序，专为局域网内多种设备的实时数据接收、存储和分发而设计。

## ✨ 项目简介

NexusRecv是一个专业的桌面设备数据接收应用程序，采用现代化的技术栈和架构设计，为企业和个人用户提供可靠的设备数据管理解决方案。

### 🎯 核心特性

- **🔌 数据接收**：基于FastAPI的高性能RESTful API，支持局域网内各种设备的实时数据接收
- **🔐 身份验证**：多层安全认证机制，包括API密钥、设备标识和IP白名单验证
- **💾 数据存储**：使用SQLite数据库安全存储数据，支持ULID格式唯一ID和智能数据管理
- **📊 实时监控**：基于ttkbootstrap的现代化GUI界面，实时显示数据和系统状态
- **🔄 多进程支持**：支持GUI、命令行、配置选择三种运行模式，可同时运行多个实例
- **⚙️ 配置管理**：智能配置文件管理，防止配置冲突，支持跨进程配置同步
- **🖥️ 系统托盘**：支持最小化到系统托盘，提供便捷的后台运行和快速访问
- **📦 跨平台构建**：支持Windows exe和Linux deb包构建，提供原生用户体验


## 🚀 快速开始

### 📋 环境要求

| 项目 | 要求 | 说明 |
|------|------|------|
| **Python版本** | 3.11+ | 推荐使用最新稳定版本 |
| **操作系统** | Windows 10/11, Linux, macOS | 支持主流桌面操作系统 |
| **硬件配置** | CPU 2核+, 内存 4GB+, 磁盘 10GB+ | 基础运行要求 |
| **网络环境** | 局域网环境 | 支持多设备通信 |

### 📦 依赖安装

```bash
# 克隆项目
git clone https://github.com/your-repo/nexusrecv.git
cd nexusrecv

# 安装依赖
pip install -r requirements.txt
```

### 🎮 运行方式

#### 方式一：源码运行（开发环境）

```bash
# 🎯 配置选择界面启动（推荐新用户）
python src/webhook_server/config_selection_gui.py

# 🖥️ 直接启动GUI界面
python src/webhook_server/webhook_server_gui.py --config <配置文件路径>

# 💻 命令行模式启动服务器
python src/webhook_server/webhook_server_command.py --config <配置文件路径>
```

#### 方式二：可执行文件运行（生产环境）

```bash
# 🔨 构建可执行文件
python scripts/build/build_by_platform.py

# 🪟 Windows - 运行构建后的程序
release/windows/NexusRecv.exe

# 🐧 Linux - 运行构建后的程序
release/linux/NexusRecv/NexusRecv

# 📦 或安装deb包后运行
sudo dpkg -i release/nexusrecv-webhook-server_1.0.2_amd64.deb
nexusrecv-webhook-server
```

## 🔌 API接口说明

NexusRecv提供RESTful API接口，支持设备数据的接收、存储和查询。

### 📊 基础信息
- **基础URL**: `http://{host}:{port}`
- **数据格式**: JSON
- **认证方式**: Bearer Token / Client Key Header
- **API文档**: `http://{host}:{port}/docs` (Swagger UI)

### 🛠️ 主要接口

| 接口 | 方法 | 功能 |
|------|------|------|
| `/webhook/token` | GET | 获取访问令牌 |
| `/webhook/save` | POST | 保存设备数据 |
| `/webhook/unread` | GET | 获取未读数据 |

### 💡 快速示例

```bash
# 获取令牌
curl -H "Authorization: Bearer your_api_key" http://localhost:8000/webhook/token

# 发送数据
curl -X POST -H "Content-Type: application/json" -H "X-Client-Key: device001" \
     -d '{"content":"温度:25.6°C"}' http://localhost:8000/webhook/save

# 获取数据
curl -H "Authorization: Bearer your_token" \
     "http://localhost:8000/webhook/unread?size=10"
```

> 📚 详细的API文档请参考：[docs/md/API接口文档.md](docs/md/webhook_server/API接口文档.md)

## ⚙️ 配置文件说明

NexusRecv使用INI格式的配置文件，支持多配置文件管理和跨进程配置同步。

### 📝 基本结构

```ini
[server]
api_key = your_api_key_here
whitelist = ***********/24,127.0.0.1,*
host = 0.0.0.0
port = 8000
message_data_table_name = message_data_1
log_config_path = /path/to/log_config.ini
run_time = 07:00-23:00
time_zone = Asia/Shanghai
expire_data_days = 7
data_limit_num = 100000
app_name = webhook_server_1
enable_sql_logging = false

[client_info]
device001 = 温湿度传感器1号
device002 = 温湿度传感器2号
sensor001 = 压力传感器
```

### 📋 主要配置项

| 配置项 | 说明 |
|--------|------|
| `api_key` | API访问密钥 |
| `whitelist` | IP白名单，支持CIDR格式 |
| `host/port` | 服务器监听地址和端口 |
| `run_time` | 运行时间段（HH:MM-HH:MM） |
| `client_info` | 设备标识和描述映射 |

> 📚 详细的配置说明请参考：[docs/md/配置文件说明.md](docs/md/webhook_server/配置文件说明.md)

## ✨ 功能特性

### 🚀 核心功能
- **🔌 多设备支持**: 支持同时接收多种类型设备的数据，基于设备标识进行智能分类管理
- **⚡ 实时数据处理**: 基于FastAPI的高性能异步数据处理，毫秒级响应时间，支持高并发
- **💾 数据持久化**: 使用SQLite数据库安全存储数据，支持ULID格式的唯一消息ID和事务处理
- **🧠 智能数据管理**: 自动清理过期数据和超量数据，支持可配置的清理策略和数据归档
- **🔄 多实例运行**: 支持多配置文件并发运行，防止配置冲突和资源竞争，确保系统稳定性

### 🖥️ 用户界面
- **🎨 现代化GUI**: 基于ttkbootstrap的现代化界面设计，支持主题切换和响应式布局
- **📊 实时监控**: 实时显示接收数据、系统状态和性能指标，提供直观的数据可视化
- **⚙️ 配置管理**: 可视化配置文件管理，支持配置验证、错误提示和配置导入导出
- **🖱️ 系统托盘**: 支持最小化到系统托盘，提供便捷操作和后台运行，减少资源占用
- **📋 数据表格**: 支持双击复制数据，提供数据筛选、搜索和排序功能，增强用户体验

### 🔒 安全认证
- **🛡️ 多层认证**: API密钥认证、设备标识认证、令牌认证三重保护，确保数据安全
- **🌐 IP白名单**: 支持单个IP、网段白名单和通配符配置，灵活的网络访问控制
- **📱 设备管理**: 基于客户端标识的设备认证机制，支持设备描述映射和权限管理
- **✅ 数据验证**: 严格的数据格式验证和长度限制，防止恶意数据注入

### 🔧 系统特性
- **🌍 跨平台**: 支持Windows、Linux、macOS系统，提供原生可执行文件和统一用户体验
- **🖥️ 桌面应用**: 专为桌面环境设计的GUI应用，用户友好的交互体验和本地化支持
- **⏰ 定时任务**: 支持定时运行和自动化任务调度，时区感知的时间计算和灵活的调度策略
- **📝 日志系统**: 完整的日志记录和管理系统，支持多级日志、文件轮转和彩色输出
- **📈 性能监控**: 实时CPU、内存使用率监控，系统资源统计和性能优化建议

## 🔨 构建和打包

### 📦 快速构建

```bash
# 安装构建依赖
pip install -r scripts/build/requirements_build.txt

# 自动检测平台并构建
python scripts/build/build_by_platform.py
```

### 📁 构建输出
- **Windows**: `release/windows/NexusRecv.exe`
- **Linux**: `release/linux/NexusRecv/` 和 deb包

> 📚 详细构建指南请参考：[docs/md/打包构建指南.md](docs/md/webhook_server/打包构建指南.md)

## 📖 使用指南

### 🌐 环境要求
- **网络**: 局域网环境，设备与服务器在同一网段
- **防火墙**: 确保防火墙允许程序网络通信
- **端口**: 确保配置的端口未被占用

### 🖥️ 基本使用
1. **启动程序**: 运行配置选择界面或直接启动GUI
2. **配置服务**: 设置API密钥、IP白名单、设备标识等
3. **启动服务**: 点击"启动服务器"按钮
4. **设备接入**: 设备通过HTTP API发送数据
5. **数据查看**: 在GUI界面查看实时数据

### 📱 设备接入
- **设备标识**: 10-30字符，仅字母数字
- **请求头**: 必须包含`X-Client-Key`
- **数据格式**: JSON格式，content字段1-100字符

### 🔧 常见问题
- **网络问题**: 检查防火墙和IP白名单设置
- **配置问题**: 确保配置文件格式正确，使用UTF-8编码
- **数据问题**: 验证设备标识和数据格式

> 📚 详细使用指南请参考：[docs/md/快速使用指南.md](docs/md/webhook_server/快速使用指南.md)

## 🧪 测试说明

项目使用pytest进行测试，确保代码质量和系统稳定性。

### 🚀 运行测试

```bash
# 运行所有测试
pytest

# 运行特定测试
pytest tests/test_webhook_server.py

# 生成覆盖率报告
pytest --cov=src tests/
```

### 📋 测试分类
- **单元测试**: 测试单个函数和类
- **集成测试**: 测试模块间交互
- **API测试**: 测试HTTP接口
- **GUI测试**: 测试界面功能

> 📚 详细测试指南请参考：[docs/md/开发者文档.md](docs/md/webhook_server/开发者文档.md)

## 🏗️ 技术架构

### 💻 核心技术栈
- **后端**: FastAPI + Uvicorn (异步Web框架)
- **数据库**: SQLite (嵌入式数据库)
- **GUI**: tkinter + ttkbootstrap (现代化界面)
- **任务调度**: APScheduler (定时任务)
- **数据验证**: Pydantic (模型验证)

### 🏛️ 架构设计
```
🖥️ GUI层 (配置界面 + 主界面 + 系统托盘)
    ↓
⚙️ 业务层 (配置管理 + 数据处理 + 监控服务)
    ↓
🔌 接口层 (RESTful API + 内部接口)
    ↓
💾 数据层 (SQLite + 配置文件 + 日志)
```

### 🎯 设计原则
- **分层架构**: 清晰的模块分层
- **单例模式**: 防止多实例冲突
- **异步处理**: 高性能异步处理
- **事件驱动**: 松耦合组件通信

> 📚 详细架构说明请参考：[docs/md/开发者文档.md](docs/md/webhook_server/开发者文档.md)

## 📋 项目信息

### 📊 版本信息

| 项目 | 信息 |
|------|------|
| **📦 项目名称** | NexusRecv |
| **🔖 当前版本** | v1.0.2 |
| **📅 发布日期** | 2025-09-03 |
| **🐍 开发语言** | Python 3.11+ |
| **📄 许可协议** | Apache License 2.0 |
| **🏠 项目主页** | [GitHub Repository](https://github.com/your-repo/nexusrecv) |

### 📞 联系方式

- **📧 邮箱**: <EMAIL>
- **🐛 问题反馈**: [GitHub Issues](https://github.com/your-repo/nexusrecv/issues)
- **💡 功能建议**: [GitHub Discussions](https://github.com/your-repo/nexusrecv/discussions)

### 🤝 贡献指南

欢迎提交Issue和Pull Request来改进项目！在提交代码前，请确保：

#### ✅ 代码质量要求
1. **📐 代码规范**: 遵循PEP 8编码规范和项目代码风格
2. **🧪 测试覆盖**: 添加必要的测试用例，确保测试覆盖率≥80%
3. **📚 文档更新**: 更新相关文档和代码注释
4. **✅ 测试通过**: 确保所有现有测试通过
5. **🎯 功能完整**: 确保新功能完整可用且符合项目目标

#### 🔄 开发流程
1. **🍴 Fork项目** 到个人仓库
2. **🌿 创建功能分支** (`git checkout -b feature/AmazingFeature`)
3. **💾 提交更改** (`git commit -m 'Add some AmazingFeature'`)
4. **📤 推送到分支** (`git push origin feature/AmazingFeature`)
5. **🔀 创建Pull Request** 并详细描述更改内容

## 📈 更新日志

### 🎉 v1.0.2 (2025-09-03)
- ✨ **新增系统托盘功能**，支持最小化到托盘，提供便捷的后台运行
- 🎨 **优化GUI界面性能**和用户体验，提升界面响应速度
- 🔧 **增强配置文件验证**和错误提示，提供更友好的错误信息
- 🐛 **修复多进程配置同步问题**，确保配置一致性
- 📚 **完善API接口和文档**，提供更详细的使用说明
- 📦 **支持跨平台打包**（Windows/Linux/macOS），提供原生用户体验
- 🔒 **增强安全认证机制**，提供多层安全保护
- ⚡ **优化数据处理性能**，支持更高并发量

### 🚀 v1.0.1 (2025-06-01)
- 🎊 **初始版本发布**，项目正式开源
- 📡 **支持多设备数据接收和存储**，基础功能完整
- 🖥️ **提供GUI和命令行两种运行模式**，满足不同使用场景
- ⚙️ **实现配置管理和多实例支持**，支持复杂部署场景
- 🔌 **基础API接口实现**，提供RESTful API服务

### 🔮 未来规划
- 🌐 **Web管理界面**：提供基于Web的管理界面
- 📊 **数据可视化**：增加数据图表和统计功能
- 🔔 **消息推送**：支持邮件、短信等消息推送
- 🔄 **数据同步**：支持多实例间数据同步
- 🛡️ **HTTPS支持**：增加SSL/TLS加密支持

## 📄 许可证

本软件遵循 **Apache License 2.0** 开源协议发布。您可以自由使用、修改和分发本软件，但需要遵守相关法律法规和协议使用条款。

详细许可证内容请查看 [LICENSE](LICENSE) 文件。

---

<div align="center">

**🎉 感谢使用NexusRecv！**

如有问题或建议，欢迎通过 [GitHub Issues](https://github.com/your-repo/nexusrecv/issues) 或邮件联系我们。

[![Star](https://img.shields.io/github/stars/your-repo/nexusrecv?style=social)](https://github.com/your-repo/nexusrecv)
[![Fork](https://img.shields.io/github/forks/your-repo/nexusrecv?style=social)](https://github.com/your-repo/nexusrecv)
[![Watch](https://img.shields.io/github/watchers/your-repo/nexusrecv?style=social)](https://github.com/your-repo/nexusrecv)

</div>
