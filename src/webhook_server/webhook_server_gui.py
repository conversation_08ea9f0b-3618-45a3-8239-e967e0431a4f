"""Webhook服务器GUI主界面模块。

此模块提供了Webhook服务器的图形用户界面，包括：
- 服务器启动/停止控制
- 实时数据显示
- 配置管理
- 系统监控
"""

import argparse
import logging
import sys
from pathlib import Path
from typing import Optional

src_path = str(Path(sys._MEIPASS) if getattr(sys, 'frozen', False) else Path(__file__).parent.parent) # noqa

# 确保源代码路径在sys.path中,且在第一位
if src_path in sys.path:
    sys.path.remove(src_path)
sys.path.insert(0,src_path)

from webhook_server.gui import main_window
logger: Optional[logging.Logger] = None

if __name__ == '__main__':
    parser = argparse.ArgumentParser()
    parser.add_argument("--config", required=True, help="path to server config file")
    config_path = parser.parse_args().config
    main_app = main_window.WebhookServerGUI(config_path)
    main_app.root.mainloop()
